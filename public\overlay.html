<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TikTok Live Overlay</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: transparent;
      height: 100vh;
      width: 100vw;
      position: relative;
    }

    #actionContainer {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      pointer-events: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .action-item {
      max-width: 80%;
      max-height: 80%;
      opacity: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      /* إزالة الانتقال التلقائي لمنع الحركة غير المرغوبة */
    }

    .action-item.active {
      opacity: 1;
    }

    /* إضافة فئة منفصلة للانتقال عند الحاجة */
    .action-item.with-transition {
      transition: opacity 0.5s ease;
    }

    .action-item img, .action-item video {
      max-width: 100%;
      max-height: 100%;
      display: block;
    }

    /* تم إزالة المواضع المختلفة واستخدام الوسط فقط في .action-item */

    .audio-icon {
      width: 60px;
      height: 60px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Animaciones básicas */
    @keyframes fadeOut {
      0% { opacity: 1; transform: translate(-50%, -50%); }
      100% { opacity: 0; transform: translate(-50%, -50%); }
    }

    .fade-out {
      animation: fadeOut 0.5s ease-out forwards;
    }

    /* تأثيرات متحركة معدلة للحفاظ على الموضع */
    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {transform: translate(-50%, -50%);}
      40% {transform: translate(-50%, calc(-50% - 30px));}
      60% {transform: translate(-50%, calc(-50% - 15px));}
    }

    @keyframes shake {
      0%, 100% {transform: translate(-50%, -50%);}
      10%, 30%, 50%, 70%, 90% {transform: translate(calc(-50% - 10px), -50%);}
      20%, 40%, 60%, 80% {transform: translate(calc(-50% + 10px), -50%);}
    }

    @keyframes pulse {
      0% {transform: translate(-50%, -50%) scale(1);}
      50% {transform: translate(-50%, -50%) scale(1.1);}
      100% {transform: translate(-50%, -50%) scale(1);}
    }

    @keyframes flip {
      0% {transform: translate(-50%, -50%) perspective(400px) rotateY(0);}
      100% {transform: translate(-50%, -50%) perspective(400px) rotateY(360deg);}
    }

    @keyframes slideIn {
      0% {transform: translate(-50%, calc(-50% - 50px)); opacity: 0;}
      100% {transform: translate(-50%, -50%); opacity: 1;}
    }

    @keyframes zoomIn {
      0% {transform: translate(-50%, -50%) scale(0); opacity: 0;}
      100% {transform: translate(-50%, -50%) scale(1); opacity: 1;}
    }

    @keyframes rotateIn {
      0% {transform: translate(-50%, -50%) rotate(-200deg) scale(0); opacity: 0;}
      100% {transform: translate(-50%, -50%) rotate(0) scale(1); opacity: 1;}
    }

    @keyframes glowPulse {
      0% {box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);}
      50% {box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);}
      100% {box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);}
    }

    /* Clases de animación (sin repetición) */
    .animate-bounce {animation: bounce 1s ease;}
    .animate-shake {animation: shake 0.5s ease;}
    .animate-pulse {animation: pulse 0.5s ease;}
    .animate-flip {animation: flip 1s ease;}
    .animate-slide-in {animation: slideIn 0.5s ease;}
    .animate-zoom-in {animation: zoomIn 0.5s ease;}
    .animate-rotate-in {animation: rotateIn 0.5s ease;}
    .animate-glow {animation: glowPulse 2s ease;}

    /* Clases de animación con repetición (cuando loopAnimation está activado) */
    .loop-animation .animate-bounce {animation: bounce 1s ease infinite;}
    .loop-animation .animate-shake {animation: shake 0.5s ease infinite;}
    .loop-animation .animate-pulse {animation: pulse 0.5s ease infinite;}
    .loop-animation .animate-flip {animation: flip 1s ease infinite;}
    .loop-animation .animate-slide-in {animation: slideIn 0.5s ease infinite;}
    .loop-animation .animate-zoom-in {animation: zoomIn 0.5s ease infinite;}
    .loop-animation .animate-rotate-in {animation: rotateIn 0.5s ease infinite;}
    .loop-animation .animate-glow {animation: glowPulse 2s ease infinite;}

    /* تنسيق عرض اسم المستخدم المنضم */
    .username-display {
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 10px;
      text-shadow: 0 0 10px rgba(255, 59, 92, 0.7);
      color: #ffffff;
    }

    /* تنسيق النص المخصص */
    .custom-text {
      font-size: 1.2em;
      margin-top: 10px;
      text-align: center;
      color: #ffffff;
    }

    /* تنسيق حاوية الوسائط والنص */
    .media-text-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  </style>
</head>
<body>
  <div id="actionContainer"></div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // اتصال Socket.IO
    const socket = io({
      reconnectionAttempts: 5,
      timeout: 20000,
      forceNew: true,
      transports: ['websocket', 'polling']
    });

    // العناصر في DOM
    const actionContainer = document.getElementById('actionContainer');

    // قائمة للإجراءات في الانتظار
    let actionQueue = [];
    let availableActions = [];

    // الانضمام إلى غرفة العرض المناسبة
    let overlayId = 'default';
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('id')) {
      overlayId = urlParams.get('id');
    }
    socket.emit('joinOverlayRoom', { overlayId: overlayId });
    console.log(`انضمام إلى غرفة شاشة العرض: ${overlayId}`);

    // إعلان متغير giftMappings
    let giftMappings = [];

    // استقبال تحديثات التعيينات
    socket.on('giftMappingsUpdated', (data) => {
      console.log('تم استلام تحديثات التعيينات:', data);
      if (data && data.mappings && Array.isArray(data.mappings)) {
        giftMappings = data.mappings;
      } else if (Array.isArray(data)) {
        giftMappings = data;
      } else {
        console.warn('تم استلام بيانات ربط هدايا بتنسيق غير متوقع:', data);
      }
      console.log(`تحديث التعيينات: عدد التعيينات المستلمة = ${giftMappings.length}`);
    });

    // طلب التعيينات من الخادم عند الاتصال
    socket.on('connect', () => {
      console.log('تم الاتصال بالخادم، جاري طلب التعيينات...');
      socket.emit('getGiftMappings');
    });

    socket.on('connect', () => {
      console.log('Connected to server');
    });

    socket.on('connect_error', (err) => {
      console.error('Connection error:', err);
    });

    socket.on('disconnect', (reason) => {
      console.warn('Disconnected:', reason);
    });

    // معالجة أحداث الهدايا
    socket.on('gift', (data) => {
      console.log('Gift received:', data);

      // البحث عن إعدادات الإجراء المقابل لهذه الهدية
      findAndQueueAction(data.giftName, data.giftId);
    });

    // استقبال أحداث اختبار الهدايا
    socket.on('testGift', (data) => {
      console.log('Test gift received:', data);

      // في حالة اختبار عرض الإجراء، نتعامل معه بشكل مباشر
      if (data.testMode === true) {
        console.log('معالجة إجراء اختبار مباشرة:', data);

        // تصحيح إنشاء كائن تعيين مؤقت للاختبار
        const mediaPath = data.mediaFilePath || data.filePath;
        console.log('مسار الوسائط المستخدم في الاختبار:', mediaPath);

        // تحديد مدة العرض (إذا كانت صفر أو غير محددة، استخدم 5 ثوانٍ كقيمة افتراضية)
        const duration = parseInt(data.duration) || 5;
        console.log('مدة العرض (بالثواني):', duration);

        const testMapping = {
          eventType: data.eventType || 'gift',
          giftId: data.giftId || 'test',
          giftName: data.giftName || 'اختبار',
          nickname: data.nickname || 'مستخدم',
          actions: data.actions || [data.type || 'image'],
          duration: duration,
          // ضمان أن يتم استخدام مسار ملف واحد فقط
          mediaFilePath: mediaPath,
          filePath: mediaPath, // تخزين في كلا الحقلين للتأكد
          soundFilePath: data.soundFile,
          customText: data.customText || '',
          testMode: true
        };

        console.log('بيانات اختبار النص المخصص في testGift:', {
          hasTextAction: testMapping.actions.includes('text'),
          customText: testMapping.customText,
          actions: testMapping.actions
        });

        console.log('تم إنشاء تعيين اختبار مؤقت:', testMapping);

        // إضافة الإجراء مباشرة للعرض بدلاً من قائمة الانتظار
        setTimeout(() => {
          showAction(testMapping);
        }, 100); // تأخير قصير لضمان جاهزية الملفات
      } else {
        // تصرف عادي للبحث عن الإجراء المقابل
        findAndQueueAction(data.giftName, data.giftId);
      }
    });

    // استقبال أحداث الإجراءات من الأنواع المختلفة
    socket.on('eventAction', (data) => {
      console.log('Event action received:', data);

      // إنشاء كائن تعيين مؤقت للإجراء
      const actionMapping = {
        eventType: data.eventType || 'gift',
        giftId: data.giftId || 'event',
        giftName: data.nickname || 'حدث',
        nickname: data.nickname || '',
        actions: data.actions || [data.type || 'alert'],
        duration: parseInt(data.duration) || 5,
        mediaFilePath: data.mediaFilePath,
        soundFilePath: data.soundFilePath,
        alertMessage: data.message || 'تنبيه',
        customText: data.customText || ''
      };

      console.log('استلام بيانات الإجراء مع النص المخصص:', {
        hasTextAction: actionMapping.actions.includes('text'),
        customText: actionMapping.customText,
        actions: actionMapping.actions
      });

      console.log('تم إنشاء تعيين إجراء:', actionMapping);

      // إضافة الإجراء إلى قائمة الانتظار
      actionQueue.push(actionMapping);

      // بدء تشغيل قائمة الانتظار إذا لم تكن قيد التشغيل بالفعل
      if (actionQueue.length === 1) {
        processActionQueue();
      }
    });

    // البحث عن الإجراء المقابل لهدية معينة وإضافته إلى قائمة الانتظار
    function findAndQueueAction(giftName, giftId) {
      let mapping = null;
      // مطابقة giftId كنص
      if (giftId && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => String(m.giftId) === String(giftId));
        if (mapping) {
          console.log(`تم العثور على إجراء بواسطة معرف الهدية: ${giftId}`);
        }
      }
      // مطابقة اسم الهدية بشكل مرن
      if (!mapping && giftName && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m =>
          m.giftName &&
          m.giftName.trim().toLowerCase() === giftName.trim().toLowerCase()
        );
        if (mapping) {
          console.log(`تم العثور على إجراء بواسطة اسم الهدية: ${giftName}`);
        }
      }
      // مطابقة عبر مكتبة الهدايا إذا لم يتم العثور
      if (!mapping && typeof availableGifts !== 'undefined' && Array.isArray(availableGifts)) {
        const giftObj = availableGifts.find(g =>
          String(g.id) === String(giftId) ||
          (g.name && g.name.trim().toLowerCase() === (giftName ? giftName.trim().toLowerCase() : ''))
        );
        if (giftObj) {
          mapping = giftMappings.find(m => String(m.giftId) === String(giftObj.id));
          if (mapping) {
            console.log('تم العثور على إجراء عبر مكتبة الهدايا:', mapping);
          }
        }
      }
      // البحث عن "أي هدية" إذا لم يتم العثور على مطابقة محددة
      if (!mapping && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.giftId === 'any');
        if (mapping) {
          console.log(`تم العثور على إجراء لـ "أي هدية"`);
        }
      }
      if (mapping) {
        console.log('تم العثور على إجراء:', mapping);
        // إضافة الإجراء إلى قائمة الانتظار
        actionQueue.push(mapping);
        // بدء تشغيل قائمة الانتظار إذا لم تكن قيد التشغيل بالفعل
        if (actionQueue.length === 1) {
          processActionQueue();
        }
      } else {
        console.log(`لم يتم العثور على إجراء للهدية: ${giftName} (ID: ${giftId})`);
      }
    }

    // معالجة قائمة انتظار الإجراءات
    function processActionQueue() {
      if (actionQueue.length === 0) {
        return;
      }

      const currentAction = actionQueue[0];
      showAction(currentAction);

      // إزالة الإجراء من القائمة بعد المدة المحددة
      setTimeout(() => {
        actionQueue.shift();

        // إخفاء الإجراء الحالي
        const actionElement = document.querySelector('.action-item.active');
        if (actionElement) {
          actionElement.classList.remove('active');

          // إزالة العنصر بعد انتهاء التأثير البصري
          setTimeout(() => {
            if (actionElement && actionElement.parentNode) {
              actionElement.remove();
            }

            // معالجة الإجراء التالي في القائمة
            if (actionQueue.length > 0) {
              processActionQueue();
            }
          }, 500);
        } else {
          // معالجة الإجراء التالي في القائمة
          if (actionQueue.length > 0) {
            processActionQueue();
          }
        }
      }, currentAction.duration * 1000 || 5000);
    }

    // متغيرات عامة للإعدادات
    let overlaySettings = {
      enableAnimations: true,
      animationType: 'zoom-in',
      loopAnimation: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      accentColor: '#ff3b5c',
      fontFamily: "'Tajawal', sans-serif",
      fontSize: 16,
      opacity: 80
    };

    // استقبال تحديثات إعدادات الـ overlay
    socket.on('overlaySettingsUpdated', (data) => {
      console.log('تم استلام تحديثات إعدادات الـ overlay:', data);
      if (data && data.settings) {
        overlaySettings = data.settings;
        console.log('تم تحديث إعدادات الـ overlay:', overlaySettings);

        // تطبيق تكرار التأثير المتحرك على العناصر الموجودة
        const actionItems = document.querySelectorAll('.action-item');
        actionItems.forEach(item => {
          if (overlaySettings.enableAnimations && overlaySettings.loopAnimation) {
            item.classList.add('loop-animation');
          } else {
            item.classList.remove('loop-animation');
          }
        });
      }
    });

    // طلب الإعدادات الحالية عند التحميل
    socket.on('connect', () => {
      socket.emit('getDisplaySettings');
      socket.emit('getGiftMappings'); // طلب تعيينات الأحداث
    });

    // دالة لاختيار نوع الحركة بشكل عشوائي
    function getRandomAnimation() {
      // تحديد نوع الحركة المتحركة من الإعدادات
      if (overlaySettings.animationType && overlaySettings.animationType !== 'random') {
        return 'animate-' + overlaySettings.animationType;
      }

      // اختيار حركة عشوائية
      const animations = [
        'animate-pulse',    // نبض
        'animate-glow'      // توهج
      ];

      // إضافة حركات أكثر تعقيدًا إذا كانت الحركات المتحركة مفعلة بالكامل
      if (overlaySettings.enableAnimations) {
        animations.push(
          'animate-bounce',     // ارتداد
          'animate-shake',      // اهتزاز
          'animate-flip',       // قلب
          'animate-slide-in',   // انزلاق
          'animate-zoom-in',    // تكبير
          'animate-rotate-in'   // دوران
        );
      }

      return animations[Math.floor(Math.random() * animations.length)];
    }

    // عرض الإجراء في الواجهة
    function showAction(mapping) {
      try {
        console.log('بدء عرض الإجراء:', mapping);
        const actions = mapping.actions || [mapping.action || 'alert'];
        let actionElement = null;

        // محاكاة ضغط مفتاح إذا كان موجودًا
        if (actions.includes('keypress') && mapping.keypressInfo) {
          simulateKeypress(mapping.keypressInfo);
        }

        // عرض تنبيه بسيط إذا كان مطلوباً أو عرض نص مخصص فقط
        if (actions.includes('alert') || (actions.includes('text') && mapping.customText && !actions.includes('image') && !actions.includes('video'))) {
          console.log('عرض تنبيه أو نص مخصص فقط');
          actionElement = document.createElement('div');
          actionElement.className = 'action-item';

          // إذا كان نوع الحدث هو انضمام، نعرض اسم المستخدم المنضم
          if (mapping.eventType === 'join' && mapping.nickname) {
            const usernameElement = document.createElement('div');
            usernameElement.className = 'username-display';
            usernameElement.textContent = mapping.nickname;
            actionElement.appendChild(usernameElement);
          }

          // عرض النص المخصص إذا كان موجوداً
          if (actions.includes('text') && mapping.customText) {
            console.log('إضافة نص مخصص في وضع التنبيه:', mapping.customText);
            const customTextElement = document.createElement('div');
            customTextElement.className = 'custom-text';
            customTextElement.textContent = mapping.customText;
            customTextElement.style.fontSize = '1.3em';
            customTextElement.style.padding = '10px';
            customTextElement.style.margin = '10px 0';
            customTextElement.style.color = '#ffffff';
            customTextElement.style.fontWeight = 'bold';
            customTextElement.style.textShadow = '1px 1px 2px black';
            actionElement.appendChild(customTextElement);
          }
          // عرض رسالة التنبيه إذا كان مطلوباً وليس هناك نص مخصص
          else if (actions.includes('alert')) {
            const alertElement = document.createElement('div');
            alertElement.textContent = mapping.alertMessage || 'تنبيه بسيط';
            actionElement.appendChild(alertElement);
          }

          // تطبيق الإعدادات المخصصة
          applyCustomStyles(actionElement);

          actionContainer.appendChild(actionElement);

          // إضافة الحركة المتحركة
          if (overlaySettings.enableAnimations) {
            // اختيار حركة عشوائية أو استخدام الحركة المحددة
            const animationClass = getRandomAnimation();
            actionElement.classList.add(animationClass);
          }

          // Añadir transición solo si las animaciones están habilitadas
          if (overlaySettings.enableAnimations) {
            actionElement.style.transition = 'opacity 0.5s ease';
          } else {
            actionElement.style.transition = 'opacity 0.1s ease';
          }

          setTimeout(() => {
            actionElement.classList.add('active');
          }, 100);
        } else {
          // صورة أو فيديو
          if (mapping.filePath || mapping.mediaFilePath) {
            actionElement = document.createElement('div');
            actionElement.className = 'action-item';

            // إنشاء حاوية للوسائط والنص
            const mediaTextContainer = document.createElement('div');
            mediaTextContainer.className = 'media-text-container';
            actionElement.appendChild(mediaTextContainer);

            let mediaPath = mapping.filePath || mapping.mediaFilePath;
            if (mediaPath && !mediaPath.startsWith('http') && !mediaPath.startsWith('/')) {
              mediaPath = '/' + mediaPath;
            }

            console.log('مسار الوسائط المستخدم:', mediaPath);

            // إذا كان نوع الحدث هو انضمام، نعرض اسم المستخدم المنضم
            if (mapping.eventType === 'join' && mapping.nickname) {
              const usernameElement = document.createElement('div');
              usernameElement.className = 'username-display';
              usernameElement.textContent = mapping.nickname;
              mediaTextContainer.appendChild(usernameElement);
            }

            if (mediaPath && (mediaPath.match(/\.(jpg|jpeg|png|gif|webp)$/i) || actions.includes('image'))) {
              console.log('إنشاء عنصر صورة مع المسار:', mediaPath);
              const img = document.createElement('img');
              img.src = mediaPath;
              img.alt = mapping.giftName || 'صورة';
              img.onerror = function() {
                console.error('خطأ في تحميل الصورة:', mediaPath);
                this.src = '/placeholder.png';
              };
              img.onload = function() {
                console.log('تم تحميل الصورة بنجاح:', mediaPath);
              };
              mediaTextContainer.appendChild(img);

              // إضافة تأثير متحرك للصور
              if (overlaySettings.enableAnimations) {
                img.style.transition = 'all 0.3s ease';

                // إضافة تأثير التوهج للصور
                if (Math.random() > 0.5) {
                  img.classList.add('animate-glow');
                }
              }

            } else if (mediaPath && (mediaPath.match(/\.(mp4|webm|ogg|mov)$/i) || actions.includes('video'))) {
              console.log('إنشاء عنصر فيديو مع المسار:', mediaPath);
              const video = document.createElement('video');
              video.src = mediaPath;
              video.autoplay = true;
              video.loop = mapping.loop || false;
              video.muted = mapping.muted || false;
              video.controls = true; // إضافة عناصر التحكم للتشخيص
              video.onerror = function(e) {
                console.error('خطأ في تحميل الفيديو:', mediaPath, e);
              };
              video.onloadeddata = function() {
                console.log('تم تحميل الفيديو بنجاح:', mediaPath);
              };
              mediaTextContainer.appendChild(video);
            }

            // إضافة النص المخصص إذا كان موجوداً
            if (actions.includes('text') && mapping.customText) {
              console.log('إضافة نص مخصص:', mapping.customText);
              const customTextElement = document.createElement('div');
              customTextElement.className = 'custom-text';
              customTextElement.textContent = mapping.customText;
              customTextElement.style.marginTop = '10px';
              customTextElement.style.padding = '5px';
              customTextElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
              customTextElement.style.borderRadius = '5px';
              customTextElement.style.color = '#ffffff';
              customTextElement.style.fontWeight = 'bold';
              customTextElement.style.textShadow = '1px 1px 2px black';
              mediaTextContainer.appendChild(customTextElement);
            } else {
              console.log('لا يوجد نص مخصص أو لم يتم تحديد إجراء النص', {
                hasTextAction: actions.includes('text'),
                customText: mapping.customText
              });
            }

            // جعل خلفية العنصر شفافة تماماً
            actionElement.style.backgroundColor = 'transparent';
            actionElement.style.border = 'none';
            actionElement.style.boxShadow = 'none';
            actionElement.style.padding = '0';

            // Ya no es necesario añadir clase de posición, está en el CSS base
            actionContainer.appendChild(actionElement);

            // Primero hacer el elemento visible inmediatamente para evitar el "salto"
            actionElement.classList.add('active');

            // Luego, si las animaciones están habilitadas, aplicar la clase de transición y animaciones
            if (overlaySettings.enableAnimations) {
              // Aplicar la clase de transición
              actionElement.classList.add('with-transition');

              // Aplicar la clase de repetición si está activada
              if (overlaySettings.loopAnimation === true) {
                actionElement.classList.add('loop-animation');
              }

              // Aplicar la animación seleccionada
              const animationClass = getRandomAnimation();
              actionElement.classList.add(animationClass);
            }
          }
        }

        // صوت
        if (actions.includes('sound') || mapping.soundFile || mapping.soundFilePath) {
          let soundPath = mapping.soundFile || mapping.soundFilePath;
          if (soundPath && !soundPath.startsWith('http') && !soundPath.startsWith('/')) {
            soundPath = '/' + soundPath;
          }

          // التحقق مما إذا كان الصوت يجب تشغيله على شاشة العرض
          // إذا كان خيار تشغيل الصوت على الخادم مفعلاً، لا نشغل الصوت هنا
          if (mapping.playSoundOnServer !== true) {
            console.log(`تشغيل الصوت على شاشة العرض: ${soundPath}`);

            const audio = document.createElement('audio');
            audio.src = soundPath;
            audio.autoplay = true;
            audio.style.display = 'none';

            // تطبيق مستوى الصوت من الإعدادات
            if (overlaySettings.sound && typeof overlaySettings.sound.volume === 'number') {
              audio.volume = overlaySettings.sound.volume / 100;
            }

            if (actionElement) {
              actionElement.appendChild(audio);
            } else {
              document.body.appendChild(audio);
            }

            audio.play().catch(err => console.error('فشل تشغيل الصوت:', err));
          } else {
            console.log(`تم تجاهل تشغيل الصوت على شاشة العرض لأنه سيتم تشغيله على الخادم: ${soundPath}`);
          }
        }

        // إزالة العنصر بعد انتهاء المدة
        if (actionElement) {
          let duration = parseInt(mapping.duration || 5);
          if (isNaN(duration) || duration < 1) duration = 5;

          setTimeout(() => {
            if (actionElement && actionElement.parentNode) {
              // إزالة الحركات المتحركة قبل الإخفاء
              const animations = [
                'animate-bounce', 'animate-shake', 'animate-pulse',
                'animate-flip', 'animate-slide-in', 'animate-zoom-in',
                'animate-rotate-in', 'animate-glow'
              ];

              animations.forEach(anim => {
                if (actionElement.classList.contains(anim)) {
                  actionElement.classList.remove(anim);
                }
              });

              actionElement.classList.remove('active');
              actionElement.classList.add('fade-out');

              setTimeout(() => {
                if (actionElement && actionElement.parentNode) {
                  actionElement.remove();
                }
              }, 500);
            }
          }, duration * 1000);
        }
      } catch (error) {
        console.error('خطأ في عرض الإجراء:', error);
      }
    }

    // تطبيق الإعدادات المخصصة على العنصر
    function applyCustomStyles(element) {
      if (!element) return;

      // تطبيق الخلفية مع الشفافية
      if (overlaySettings.backgroundColor) {
        // استخدام قيمة شفافية الخلفية المخصصة إذا كانت موجودة
        console.log('تطبيق شفافية الخلفية:', overlaySettings.backgroundOpacity);

        // التأكد من أن القيمة رقمية
        let bgOpacity = 80;
        if (typeof overlaySettings.backgroundOpacity === 'number') {
          bgOpacity = overlaySettings.backgroundOpacity;
        } else if (overlaySettings.backgroundOpacity !== undefined) {
          bgOpacity = parseInt(overlaySettings.backgroundOpacity, 10);
          if (isNaN(bgOpacity)) bgOpacity = 80;
        } else if (typeof overlaySettings.opacity === 'number') {
          bgOpacity = overlaySettings.opacity;
        } else if (overlaySettings.opacity !== undefined) {
          bgOpacity = parseInt(overlaySettings.opacity, 10);
          if (isNaN(bgOpacity)) bgOpacity = 80;
        }

        const opacity = bgOpacity / 100;
        const bgColor = hexToRgba(overlaySettings.backgroundColor, opacity);
        element.style.backgroundColor = bgColor;

        console.log('تم تطبيق لون الخلفية:', bgColor, 'مع شفافية:', bgOpacity);
      }

      // تطبيق لون النص
      if (overlaySettings.textColor) {
        element.style.color = overlaySettings.textColor;
      }

      // تطبيق نوع الخط
      if (overlaySettings.fontFamily) {
        element.style.fontFamily = overlaySettings.fontFamily;
      }

      // تطبيق حجم الخط
      if (overlaySettings.fontSize) {
        element.style.fontSize = `${overlaySettings.fontSize}px`;
      }

      // إضافة تأثيرات إضافية
      element.style.borderRadius = '10px';
      element.style.padding = '15px';
      element.style.boxShadow = `0 5px 15px rgba(0, 0, 0, 0.3)`;

      // إضافة حدود ملونة
      if (overlaySettings.accentColor) {
        element.style.border = `2px solid ${overlaySettings.accentColor}`;
      }
    }

    // تحويل لون HEX إلى RGBA
    function hexToRgba(hex, opacity) {
      if (!hex) return `rgba(0, 0, 0, ${opacity})`;

      hex = hex.replace('#', '');
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    socket.on('available-actions', (data) => {
      console.log('available-actions', data);
      if (Array.isArray(data)) {
        availableActions = data;
      } else {
        availableActions = [];
      }
    });

    // استقبال تعيينات الأحداث
    socket.on('giftMappings', (data) => {
      console.log('تم استلام تعيينات الأحداث:', data);
      if (data && data.mappings) {
        giftMappings = data.mappings;
      }
    });

    socket.on('gift', (data) => {
      console.log('gift', data);

      if (data.action && data.availableOverlays && Array.isArray(data.action) && data.action.length > 0) {
        displayActions(data);
      }
    });

    function displayActions(data) {
      const { action, availableOverlays } = data;

      // تخزين رقم الـ overlay المحدد
      const overlayNumber = data.overlayNumber || 1;

      // التحقق إذا كان هذا الـ overlay هو المقصود
      if (overlayNumber != overlayId) {
        console.log(`هذا الإجراء مخصص للـ Overlay رقم ${overlayNumber}، بينما هذا هو رقم ${overlayId}`);
        return;
      }

      // البحث عن الإجراءات المناسبة
      if (Array.isArray(action) && Array.isArray(availableActions)) {
        const foundActions = action.map(actionId => {
          return availableActions.find(a => a.id === actionId);
        }).filter(Boolean);

        console.log('الإجراءات التي سيتم عرضها:', foundActions);

        foundActions.forEach(foundAction => {
          if (foundAction) {
            const actionElement = document.createElement('div');
            actionElement.className = 'action-item';

            // جعل العنصر شفافًا تمامًا (بدون خلفية أو إطار)
            actionElement.style.backgroundColor = 'transparent';
            actionElement.style.border = 'none';
            actionElement.style.boxShadow = 'none';
            actionElement.style.padding = '0';

            // Ya no es necesario añadir clase de posición, está en el CSS base

            if (foundAction.mediaType === 'image') {
              const img = document.createElement('img');
              img.src = foundAction.mediaUrl;
              actionElement.appendChild(img);
            } else if (foundAction.mediaType === 'video') {
              const video = document.createElement('video');
              video.src = foundAction.mediaUrl;
              video.autoplay = true;
              video.loop = foundAction.loop;
              video.muted = false;
              actionElement.appendChild(video);
            } else if (foundAction.mediaType === 'audio') {
              const audio = document.createElement('audio');
              audio.src = foundAction.mediaUrl;
              audio.autoplay = true;

              // إظهار أيقونة صوت لتمثيل الملف الصوتي
              const audioIcon = document.createElement('div');
              audioIcon.className = 'audio-icon';
              audioIcon.innerHTML = '<svg viewBox="0 0 24 24" width="64" height="64"><path fill="white" d="M3,9v6h4l5,5V4L7,9H3z M16.5,12c0-1.77-1.02-3.29-2.5-4.03v8.05C15.48,15.29,16.5,13.77,16.5,12z"></path></svg>';
              actionElement.appendChild(audioIcon);

              actionElement.appendChild(audio);

              // إزالة العنصر بعد انتهاء الصوت
              audio.onended = function() {
                if (actionContainer.contains(actionElement)) {
                  actionContainer.removeChild(actionElement);
                }
              };
            }

            actionContainer.appendChild(actionElement);

            // تفعيل التأثير - جعل العنصر مرئيًا فورًا
            actionElement.classList.add('active');

            // إضافة التأثيرات المتحركة إذا كانت مفعلة
            if (overlaySettings.enableAnimations) {
              // إضافة فئة الانتقال
              actionElement.classList.add('with-transition');

              // إضافة فئة التكرار إذا كانت مفعلة
              if (overlaySettings.loopAnimation === true) {
                actionElement.classList.add('loop-animation');
              }

              // إضافة تأثير متحرك
              const animationClass = getRandomAnimation();
              actionElement.classList.add(animationClass);
            }

            // إزالة العنصر بعد المدة المحددة إذا لم تكن ملف صوتي
            if (foundAction.mediaType !== 'audio') {
              const duration = foundAction.mediaType === 'video' ? 30000 : 6000;
              setTimeout(() => {
                if (actionContainer.contains(actionElement)) {
                  actionElement.classList.add('fade-out');
                  actionElement.classList.remove('active');
                  setTimeout(() => {
                    if (actionContainer.contains(actionElement)) {
                      actionContainer.removeChild(actionElement);
                    }
                  }, 500);
                }
              }, duration);
            }
          }
        });
      }
    }

    // محاكاة ضغط مفتاح
    function simulateKeypress(keypressInfo) {
      if (!keypressInfo) return;

      try {
        console.log('محاكاة ضغط مفتاح:', keypressInfo);

        let key = keypressInfo.key || '';
        const modifiers = keypressInfo.modifiers || {};
        const isCustom = keypressInfo.isCustom || false;

        // تحويل مفاتيح خاصة
        if (!isCustom) {
          switch (key) {
            case 'space':
              key = ' ';
              break;
            case 'enter':
              key = 'Enter';
              break;
            case 'escape':
              key = 'Escape';
              break;
            case 'tab':
              key = 'Tab';
              break;
            case 'arrowup':
              key = 'ArrowUp';
              break;
            case 'arrowdown':
              key = 'ArrowDown';
              break;
            case 'arrowleft':
              key = 'ArrowLeft';
              break;
            case 'arrowright':
              key = 'ArrowRight';
              break;
            case 'backspace':
              key = 'Backspace';
              break;
            // المزيد من المفاتيح الخاصة يمكن إضافتها هنا
          }
        }

        // إنشاء حدث keydown
        const keydownEvent = new KeyboardEvent('keydown', {
          key: key,
          code: getKeyCode(key),
          ctrlKey: modifiers.ctrl || false,
          altKey: modifiers.alt || false,
          shiftKey: modifiers.shift || false,
          bubbles: true
        });

        // إنشاء حدث keyup
        const keyupEvent = new KeyboardEvent('keyup', {
          key: key,
          code: getKeyCode(key),
          ctrlKey: modifiers.ctrl || false,
          altKey: modifiers.alt || false,
          shiftKey: modifiers.shift || false,
          bubbles: true
        });

        // إرسال الأحداث
        document.dispatchEvent(keydownEvent);

        // تأخير قصير بين keydown و keyup
        setTimeout(() => {
          document.dispatchEvent(keyupEvent);
        }, 50);

        console.log('تم محاكاة ضغط المفتاح:', key);
      } catch (error) {
        console.error('خطأ في محاكاة ضغط المفتاح:', error);
      }
    }

    // استخراج كود المفتاح من المفتاح
    function getKeyCode(key) {
      // المفاتيح الخاصة الشائعة
      const keyCodes = {
        ' ': 'Space',
        'Enter': 'Enter',
        'Escape': 'Escape',
        'Tab': 'Tab',
        'ArrowUp': 'ArrowUp',
        'ArrowDown': 'ArrowDown',
        'ArrowLeft': 'ArrowLeft',
        'ArrowRight': 'ArrowRight',
        'Backspace': 'Backspace',
        'Delete': 'Delete',
        'Control': 'ControlLeft',
        'Alt': 'AltLeft',
        'Shift': 'ShiftLeft',
        'CapsLock': 'CapsLock',
        'Home': 'Home',
        'End': 'End',
        'PageUp': 'PageUp',
        'PageDown': 'PageDown'
      };

      // التحقق من وجود المفتاح في القائمة
      if (key in keyCodes) {
        return keyCodes[key];
      }

      // للمفاتيح العادية (حرف أو رقم)
      if (key.length === 1) {
        if (/[a-z]/i.test(key)) {
          return 'Key' + key.toUpperCase();
        } else if (/[0-9]/.test(key)) {
          return 'Digit' + key;
        }
      }

      // إذا كان مفتاح وظيفي (F1-F12)
      if (/^F\d{1,2}$/.test(key)) {
        return key;
      }

      // إذا لم يتم التعرف على المفتاح
      return 'Unidentified';
    }
  </script>
  <script src="/js/translation.js"></script>
</body>
</html>