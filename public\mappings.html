<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ربط الهدايا بالإجراءات</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
    }

    /* دعم الوضع المظلم للـ sidebar */
    [data-theme="dark"] .sidebar {
      background: linear-gradient(180deg, rgba(26, 26, 26, 0.95) 0%, rgba(37, 37, 37, 0.95) 100%);
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      margin-bottom: 30px;
      /* استخدام rgba بدلاً من opacity لتجنب تأثير النوافذ المنبثقة */
      background-color: rgba(255, 255, 255, 0.95);
    }

    /* دعم الوضع المظلم */
    [data-theme="dark"] .container {
      background-color: rgba(30, 30, 30, 0.95);
    }
    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }
    h3 {
      color: var(--text-secondary);
      font-size: 1.3rem;
      margin-top: 25px;
      font-weight: 500;
    }
    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }
    button {
      background: var(--primary-gradient);
      color: var(--button-text);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
    }
    button:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }
    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(255, 59, 92, 0.25);
    }
    button.secondary {
      background-color: #6c757d;
      box-shadow: 0 4px 6px rgba(108, 117, 125, 0.2);
    }
    button.secondary:hover {
      background-color: #5a6268;
      box-shadow: 0 6px 8px rgba(108, 117, 125, 0.25);
    }
    button.small {
      padding: 2px 6px; /* زيادة التباعد الأفقي من 5px إلى 6px */
      font-size: 13px; /* زيادة حجم الخط من 12px إلى 13px */
      min-width: 35px; /* زيادة العرض الأدنى من 32px إلى 35px */
      margin: 0px; /* إزالة الهوامش */
      border-radius: 3px; /* زيادة حجم الزوايا من 2px إلى 3px */
      line-height: 1.2; /* زيادة ارتفاع السطر من 1.1 إلى 1.2 */
      height: 24px; /* زيادة ارتفاع الأزرار من 22px إلى 24px */
    }
    .buttons-container {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      justify-content: center;
    }

    /* أنماط التعيينات المعطلة */
    .mapping-disabled {
      background-color: rgba(108, 117, 125, 0.08) !important;
      border-left: 4px solid #6c757d !important;
      position: relative;
    }

    .mapping-disabled td {
      opacity: 0.7;
      color: #6c757d !important;
    }

    .mapping-disabled .badge.bg-secondary {
      background-color: #6c757d !important;
      color: white !important;
      font-size: 11px;
    }

    .mapping-disabled .badge.bg-success {
      background-color: #198754 !important;
      color: white !important;
      font-size: 11px;
    }

    .mapping-disabled button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: #e9ecef !important;
      border-color: #dee2e6 !important;
      color: #6c757d !important;
    }

    .mapping-disabled .gift-icon {
      opacity: 0.6;
      filter: grayscale(50%);
    }

    .mapping-disabled .gift-name {
      text-decoration: line-through;
      opacity: 0.8;
    }
    .gift-mappings {
      width: 100%;
      border-collapse: collapse; /* تغيير من separate إلى collapse لإزالة المسافات بين الخلايا */
      border-spacing: 0; /* إزالة المسافة بين الخلايا */
      margin-top: 10px; /* تقليل الهامش العلوي من 20px إلى 10px */
      background-color: var(--section-bg);
      border-radius: 4px; /* تقليل حجم الزوايا من 10px إلى 4px */
      overflow: hidden;
      box-shadow: 0 0 3px var(--shadow-color); /* تقليل حجم الظل من 10px إلى 3px */
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid var(--border-color); /* إضافة حدود للجدول */
    }
    .gift-mappings th, .gift-mappings td {
      padding: 2px; /* تقليل التباعد الداخلي */
      text-align: center; /* توسيط المحتوى */
      border-bottom: 1px solid var(--border-color);
      border-right: 1px solid var(--border-color); /* حدود على الجانب الأيمن */
      transition: border-color 0.3s ease;
      font-size: 0.9rem; /* تقليل حجم الخط */
      line-height: 1.1; /* تقليل ارتفاع السطر */
      height: 36px; /* توحيد ارتفاع الخلايا */
      max-height: 36px; /* توحيد أقصى ارتفاع الخلايا */
      overflow: hidden; /* إخفاء المحتوى الزائد */
      vertical-align: middle; /* محاذاة عمودية في المنتصف */
      position: relative; /* إضافة موضع نسبي للخلية */
    }

    /* تأكيد على محاذاة المحتوى في وسط الخلية */
    .gift-mappings td > * {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .gift-mappings th:last-child, .gift-mappings td:last-child {
      border-right: none; /* إزالة الحدود من الخلية الأخيرة */
    }
    .gift-mappings th {
      background-color: var(--table-header-bg);
      font-weight: 600;
      color: var(--text-color);
      text-align: center;
      padding: 2px; /* تقليل التباعد الداخلي */
      font-size: 0.9rem; /* تقليل حجم الخط */
      height: 30px; /* توحيد ارتفاع العناوين مع الخلايا */
      vertical-align: middle; /* محاذاة عمودية في المنتصف */
    }
    .gift-mappings tr:last-child td {
      border-bottom: none;
    }
    .gift-mappings tr:hover {
      background-color: var(--table-row-hover);
    }
    .gift-info {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 5px;
    }

    .gift-name {
      font-weight: bold;
      color: var(--text-color);
      font-size: 14px;
      display: block;
      width: 100%;
      text-align: center;
    }

    .gift-subname {
      color: var(--text-secondary);
      font-size: 12px;
      display: block;
      width: 100%;
      text-align: center;
      margin-top: 3px;
    }
    .gift-icon {
      width: 32px;
      height: 32px;
      margin-left: 10px;
      border-radius: 5px;
      object-fit: contain;
    }
    .actions-cell {
      display: flex;
      gap: 1px; /* تقليل المسافة بين الأزرار */
      justify-content: center; /* توسيط الأزرار أفقيًا */
      align-items: center; /* توسيط الأزرار عموديًا */
      flex-wrap: wrap; /* السماح بالتفاف الأزرار إذا لزم الأمر */
      padding: 0; /* إزالة التباعد الداخلي */
      height: 100%; /* استخدام كامل ارتفاع الخلية */
      width: 100%; /* استخدام كامل عرض الخلية */
    }

    /* تصغير أزرار الخيارات */
    .actions-cell button.small {
      padding: 1px 4px; /* تقليل التباعد الداخلي */
      font-size: 0.8rem; /* تقليل حجم الخط */
      height: 24px; /* تعديل الارتفاع */
      min-width: 40px; /* تحديد أقل عرض */
      margin: 0 1px; /* إضافة هوامش صغيرة */
      display: inline-flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px; /* تقليل حجم الزوايا */
    }

    /* تحسين مظهر الأزرار عند التحويم */
    .actions-cell button.small:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    }
    .dialog {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    .dialog-content {
      background-color: var(--section-bg);
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      width: 90%;
      max-width: 500px;
    }
    .dialog h3 {
      margin-top: 0;
      margin-bottom: 20px;
    }
    .form-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
    }
    input, select {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--input-border);
      background-color: var(--input-bg);
      color: var(--text-color);
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s;
      font-family: 'Tajawal', sans-serif;
    }
    input:focus, select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    .alert {
      background-color: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }
    .alert.success {
      background-color: #d4edda;
      color: #155724;
    }

    /* أنماط لرفع الملفات */
    .file-upload {
      border: 2px dashed var(--border-color);
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      background-color: var(--table-header-bg);
    }

    .file-upload:hover {
      border-color: var(--primary-color);
      background-color: var(--hover-bg);
    }

    .file-preview {
      margin-top: 15px;
      max-width: 100%;
      max-height: 150px;
      border-radius: 5px;
      display: none;
    }

    .file-preview.active {
      display: block;
    }

    .file-info {
      margin-top: 5px;
      font-size: 14px;
      color: var(--text-secondary);
    }

    /* أنماط لوقت العرض */
    .duration-slider {
      display: flex;
      align-items: center;
      gap: 15px;
      width: 100%;
    }

    .duration-slider input[type="range"] {
      flex: 1;
    }

    .duration-value {
      min-width: 50px;
      text-align: center;
      font-weight: 500;
      color: var(--text-secondary);
    }

    /* أنماط لاختبار الإجراء */
    .test-action {
      margin-top: 15px;
      padding: 15px;
      background-color: var(--table-header-bg);
      border-radius: 8px;
      border: 1px solid var(--border-color);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .test-action h4 {
      margin-top: 0;
      margin-bottom: 15px;
      color: var(--text-color);
    }

    /* أنماط زر التحكم في اختبار الأحداث */
    #toggle-event-testing {
      background: var(--accent-color);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-family: inherit;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    #toggle-event-testing:hover {
      background: var(--accent-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    #toggle-event-testing:active {
      transform: translateY(0);
    }

    /* أنماط بطاقات اختبار الأحداث */
    .test-event-card {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .test-event-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      border-color: var(--primary-color);
    }

    .test-event-card .btn {
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .test-event-card .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .event-testing-container {
      animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* تحسين مظهر الحقول في بطاقات الاختبار */
    .test-event-card .form-control {
      border-radius: 6px;
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }

    .test-event-card .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(255, 59, 92, 0.1);
    }

    .test-event-card label {
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 5px;
    }

    /* أنماط للقائمة المنسدلة للروابط */
    .overlay-dropdown {
      display: flex;
      align-items: center;
      margin-top: 20px;
      gap: 15px;
    }

    .overlay-dropdown select {
      flex: 1;
    }

    .overlay-dropdown a {
      text-decoration: none;
    }

    .overlay-container {
      margin-top: 30px;
      padding: 20px;
      background-color: var(--table-header-bg);
      border-radius: 8px;
      border: 1px solid var(--border-color);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .overlay-title {
      color: var(--text-color);
      font-size: 1.2rem;
      margin-bottom: 15px;
      font-weight: 500;
    }

    .overlay-options {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    /* أنماط للاختيارات المتعددة */
    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
    }

    .form-check {
      display: inline-flex;
      align-items: center;
      margin-bottom: 8px;
      margin-left: 15px;
      padding: 8px 15px;
      background-color: var(--table-header-bg);
      border-radius: 5px;
      transition: all 0.2s;
      cursor: pointer;
      min-width: 140px;
      flex-direction: row-reverse;
      justify-content: flex-end;
      border: 1px solid var(--border-color);
    }

    .form-check:hover {
      background-color: var(--hover-bg);
    }

    .form-check-input {
      margin-right: 10px;
      margin-left: 0;
      width: 18px;
      height: 18px;
      position: relative;
    }

    .form-check-label {
      margin-bottom: 0;
      cursor: pointer;
      font-size: 14px;
      font-weight: normal;
      color: var(--text-color);
      margin-right: 0;
      padding-right: 0;
    }

    /* أنماط لرفع ملف الصوت */
    .sound-upload {
      margin-bottom: 10px;
    }

    #sound-preview {
      margin-top: 10px;
    }

    /* أنماط السحب والإفلات */
    .file-upload.highlight {
      border-color: #ff3b5c;
      background-color: #fff0f3;
      box-shadow: 0 0 10px rgba(255, 59, 92, 0.3);
    }

    /* تخصيص قائمة اختيار الهدايا */
    select#edit-gift {
      padding-right: 10px;
    }

    /* أنماط لعرض الصور في القائمة المنسدلة */
    select#edit-gift option {
      padding: 8px;
      display: flex;
      align-items: center;
    }

    /* تحسين عرض الصور في الجدول */
    .gift-icon {
      width: 32px;
      height: 32px;
      margin-left: 10px;
      border-radius: 5px;
      object-fit: contain;
      display: inline-block;
      vertical-align: middle;
    }

    /* تحسين عرض صورة الهدية في عمود نوع الهدية */
    .gift-mappings td.text-center {
      text-align: center;
      vertical-align: middle;
      position: relative; /* إضافة موضع نسبي للخلية */
      overflow: hidden; /* إخفاء أي محتوى يتجاوز حدود الخلية */
      background-color: var(--section-bg); /* تحديث الخلفية لتتوافق مع وضع الليل */
      transition: background-color 0.3s ease;
    }

    .gift-mappings td.text-center .gift-icon {
      width: 40px;
      height: 40px;
      margin: 0 auto;
      display: inline-block;
    }

    /* أنماط خاصة لعمود نوع الهدية */
    .gift-type-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding: 1px; /* تقليل التباعد الداخلي من 5px إلى 1px */
      width: 100%;
      text-align: center;
      position: relative; /* إضافة موضع نسبي للحاوية */
      z-index: 1; /* إضافة مستوى z-index للتأكد من أن المحتوى يظهر فوق أي شيء آخر */
      background-color: var(--section-bg); /* تحديث الخلفية لتتوافق مع وضع الليل */
      transition: background-color 0.3s ease;
      height: 26px; /* زيادة الارتفاع من 22px إلى 26px */
      overflow: hidden; /* إخفاء أي محتوى يتجاوز الارتفاع المحدد */
    }

    .gift-type-name {
      font-weight: bold;
      text-align: center;
      color: var(--text-color);
      font-size: 15px; /* زيادة حجم الخط من 14px إلى 15px */
      display: block;
      width: 100%;
      margin: 0 auto;
      line-height: 1.2; /* زيادة ارتفاع السطر من 1.1 إلى 1.2 */
    }

    .gift-type-subname {
      color: var(--text-secondary);
      font-size: 13px; /* زيادة حجم الخط من 12px إلى 13px */
      display: block;
      width: 100%;
      text-align: center;
      margin-top: 1px; /* تقليل الهامش العلوي من 3px إلى 1px */
      line-height: 1.2; /* زيادة ارتفاع السطر من 1.1 إلى 1.2 */
    }

    /* أنماط للقائمة المخصصة لاختيار الهدايا */
    .custom-select-container {
      position: relative;
      width: 100%;
      margin-bottom: 15px;
    }

    .select-selected {
      padding: 12px 15px;
      border: 1px solid var(--input-border);
      border-radius: 8px;
      background-color: var(--input-bg);
      color: var(--text-color);
      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      position: relative;
      font-size: 16px;
      transition: all 0.2s ease;
    }

    .select-selected:hover {
      background-color: var(--hover-bg);
    }

    .select-selected:after {
      content: '';
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      border-width: 6px;
      border-style: solid;
      border-color: #888 transparent transparent transparent;
      transition: all 0.2s ease;
    }

    .select-selected.select-arrow-active:after {
      border-color: transparent transparent #888 transparent;
      transform: translateY(-70%);
    }

    .select-items {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      z-index: 99;
      max-height: 350px;
      overflow-y: auto;
      background-color: var(--section-bg);
      border: 1px solid var(--input-border);
      border-radius: 0 0 8px 8px;
      box-shadow: 0 4px 12px var(--shadow-color);
      transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
      display: none; /* مخفية بشكل افتراضي */
    }

    .gift-option {
      padding: 12px 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background-color 0.2s;
      border-bottom: 1px solid var(--border-color);
      font-size: 15px;
    }

    .gift-option:hover {
      background-color: var(--hover-bg);
    }

    .gift-option.selected {
      background-color: var(--hover-bg);
      font-weight: bold;
    }

    .gift-option img {
      width: 28px;
      height: 28px;
      margin-left: 10px;
      object-fit: contain;
    }

    .select-hide {
      display: none !important;
    }

    .select-selected.select-arrow-active {
      border-radius: 8px 8px 0 0;
      border-bottom-color: transparent;
      background-color: var(--section-bg);
    }

    /* أنماط لبطاقة الهدية المختارة */
    .selected-gift-card {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: var(--table-header-bg);
      transition: background-color 0.3s ease, border-color 0.3s ease;
      margin-top: 10px;
    }

    .gift-preview-img {
      width: 48px;
      height: 48px;
      object-fit: contain;
      margin-left: 15px;
      border-radius: 6px;
    }

    .selected-gift-info {
      flex: 1;
    }

    .gift-name {
      font-size: 15px; /* زيادة حجم الخط من 14px إلى 15px */
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 0px; /* تقليل الهامش السفلي من 4px إلى 0px */
      line-height: 1.2; /* زيادة ارتفاع السطر من 1.1 إلى 1.2 */
    }

    .gift-diamonds {
      font-size: 14px;
      color: #666;
    }

    /* أنماط مكتبة الهدايا */
    .gift-library {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      background-color: #f8f9fa;
    }

    .gift-library h5 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-weight: 600;
    }

    .gift-library-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 10px;
      max-height: 300px;
      overflow-y: auto;
      padding: 5px;
      background-color: white;
      border-radius: 5px;
      border: 1px solid #eee;
    }

    .gift-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      border: 1px solid #eee;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: white;
    }

    .gift-item:hover {
      border-color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 3px 5px rgba(0,0,0,0.1);
    }

    .gift-item.selected {
      border-color: #ff3b5c;
      background-color: #fff0f3;
    }

    .gift-item img {
      width: 48px;
      height: 48px;
      object-fit: contain;
      margin-bottom: 5px;
    }

    .gift-item .gift-name {
      font-size: 12px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      margin-bottom: 2px;
    }

    .gift-item .gift-diamonds {
      font-size: 11px;
      color: #666;
    }

    .loading-gifts {
      grid-column: 1 / -1;
      text-align: center;
      padding: 20px;
      color: #666;
    }

    .search-box {
      margin-bottom: 10px;
    }

    .search-box input {
      width: 100%;
      padding: 8px 12px;
      border-radius: 5px;
      border: 1px solid #ddd;
    }

    .no-gifts-found {
      grid-column: 1 / -1;
      text-align: center;
      padding: 20px;
      color: #666;
    }

    /* أنماط لقسم محاكاة المفاتيح */
    .keypress-container {
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 8px;
      background-color: #f9f9f9;
    }

    .keypress-modifiers {
      display: flex;
      gap: 15px;
      border-top: 1px solid #eee;
      padding-top: 10px;
    }

    .key-preview {
      margin-top: 10px;
      padding: 8px 15px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: monospace;
      font-size: 14px;
      text-align: center;
    }

    .custom-key-container {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #eee;
    }

    /* تخصيص حجم وشكل نافذة الربط */
    .modal-lg {
      max-width: 900px !important;
    }

    .modal-content {
      border-radius: 12px;
      box-shadow: 0 5px 30px rgba(0, 0, 0, 0.15);
    }

    .modal-body {
      padding: 25px;
    }

    .modal-header {
      padding: 15px 25px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #eee;
    }

    /* تنسيق نافذة الحوار لوضع الليل */
    .modal-content {
      background-color: var(--section-bg);
      color: var(--text-color);
      border-color: var(--border-color);
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }

    .modal-header {
      background-color: var(--section-bg);
      border-bottom: 1px solid var(--border-color);
      color: var(--text-color);
      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    }

    .modal-body {
      background-color: var(--section-bg);
      color: var(--text-color);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .modal-footer {
      padding: 15px 25px;
      border-top: 1px solid var(--border-color);
      background-color: var(--section-bg);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    [data-theme="dark"] .btn-close {
      filter: invert(1);
    }

    .keypress-editor {
      background-color: var(--section-bg);
      border-color: var(--border-color);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    #keypress-sequence-list {
      background-color: var(--section-bg);
      border-color: var(--border-color);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    /* أنماط الشارات */
    .badge {
      display: inline-block;
      padding: 0.25em 0.6em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      color: #fff;
    }
    .badge-primary, .bg-primary {
      background-color: #007bff;
    }
    .badge-secondary, .bg-secondary {
      background-color: #6c757d;
    }
    .badge-success, .bg-success {
      background-color: #28a745;
    }
    .badge-danger, .bg-danger {
      background-color: #dc3545;
    }
    .badge-warning, .bg-warning {
      background-color: #ffc107;
      color: #212529;
    }
    .badge-info, .bg-info {
      background-color: #17a2b8;
    }

    /* أنماط عرض تفاصيل الأحداث */
    .gift-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }

    /* أنماط لعمود اسم التعيين */
    .gift-name-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      padding: 1px;
      width: 100%;
      text-align: center;
    }

    .gift-type-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }

    /* تنسيق جديد للعرض الأفقي */
    .gift-type-container.horizontal {
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      text-align: right;
      padding: 2px; /* تقليل التباعد الداخلي */
      height: auto; /* السماح بارتفاع تلقائي */
    }

    .gift-type-container.horizontal .gift-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-right: 5px; /* تقليل الهامش */
      text-align: right;
    }

    .gift-type-container.horizontal .gift-icon {
      width: 28px; /* تقليل حجم الصورة */
      height: 28px; /* تقليل حجم الصورة */
      margin-bottom: 0;
      margin-left: 5px; /* تقليل الهامش */
      object-fit: contain;
    }

    .gift-type-name {
      font-weight: bold;
      font-size: 13px; /* تقليل حجم الخط */
      line-height: 1.2; /* تقليل ارتفاع السطر */
      margin: 0; /* إزالة الهوامش */
    }

    .gift-type-subname {
      font-size: 11px; /* تقليل حجم الخط */
      color: var(--text-secondary);
      line-height: 1.1; /* تقليل ارتفاع السطر */
      margin: 0; /* إزالة الهوامش */
    }

    /* أنماط أقسام أنواع الأحداث */
    .event-type-section {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background-color: var(--table-header-bg);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .event-type-section .form-text {
      color: var(--text-secondary);
      font-size: 0.85rem;
      margin-top: 5px;
    }

    /* تنسيق النصوص المساعدة للوضع المظلم */
    .form-text.text-muted {
      color: var(--text-secondary) !important;
      transition: color 0.3s ease;
    }

    /* تنسيق خاص للنص الإنجليزي في الوضع المظلم */
    .form-text.text-muted:lang(en) {
      color: var(--text-secondary) !important;
    }

    .event-type-section .alert-info {
      background-color: rgba(0, 123, 255, 0.1);
      color: var(--text-color);
      border-color: rgba(0, 123, 255, 0.2);
    }

    /* تنسيق قسم الانضمام */
    #specific-user-section {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed var(--border-color);
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html" class="active">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>ربط الهدايا بالإجراءات</h1>

      <div class="alert" id="alertBox"></div>

      <div class="section">
        <h3>الأحداث المضبوطة</h3>
        <p>يمكنك ربط الأحداث المختلفة (الهدايا، الإعجابات، التعليقات، المتابعات، المشاركات، الانضمام) بإجراءات محددة لتشغيلها عند حدوثها.</p>

        <div class="table-container">
          <table class="gift-mappings" id="giftMappingsTable">
            <thead>
              <tr>
                <th style="width: 15%;">الاسم</th>
                <th style="width: 15%;">نوع الحدث</th>
                <th style="width: 25%;">التفاصيل</th>
                <th style="width: 15%;">الإجراءات</th>
                <th style="width: 10%;">وقت العرض</th>
                <th style="width: 20%;">خيارات</th>
              </tr>
            </thead>
            <tbody id="mappings-table-body">
              <!-- ستتم تعبئة هذا الجدول بشكل ديناميكي -->
            </tbody>
          </table>
        </div>

        <div class="buttons-container">
          <button id="add-mapping-btn">إضافة ربط جديد</button>
          <button id="updateGiftsBtn" class="secondary">تحديث قائمة الهدايا</button>
        </div>
      </div>

      <!-- زر التحكم في قسم اختبار الأحداث -->
      <div class="section">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <h3 style="margin: 0;">🧪 اختبار الأحداث</h3>
          <button id="toggle-event-testing" class="btn btn-secondary" style="padding: 8px 16px; font-size: 14px;">
            <span id="toggle-icon">👁️</span> <span id="toggle-text">إظهار</span>
          </button>
        </div>
      </div>

      <!-- قسم اختبار الأحداث -->
      <div id="event-testing-section" class="section" style="display: none;">
        <h3 style="display: none;">🧪 اختبار الأحداث</h3>
        <p>اختبر الأحداث المختلفة لمحاكاة الأحداث الحقيقية من TikTok Live والتأكد من عمل التعيينات بشكل صحيح.</p>

        <div class="event-testing-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">

          <!-- اختبار الهدايا -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">🎁</span>
              اختبار الهدايا
            </h4>
            <div class="form-group">
              <label for="test-gift-select">اختيار الهدية:</label>
              <select id="test-gift-select" class="form-control">
                <option value="">اختر هدية...</option>
              </select>
            </div>
            <div class="form-group">
              <label for="test-gift-username">اسم المستخدم:</label>
              <input type="text" id="test-gift-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <div class="form-group">
              <label for="test-gift-repeat">عدد التكرار:</label>
              <input type="number" id="test-gift-repeat" class="form-control" min="1" max="100" value="1">
            </div>
            <button id="test-gift-btn" class="btn btn-primary" style="width: 100%;">اختبار الهدية</button>
          </div>

          <!-- اختبار التعليقات -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">💬</span>
              اختبار التعليقات
            </h4>
            <div class="form-group">
              <label for="test-comment-text">نص التعليق:</label>
              <input type="text" id="test-comment-text" class="form-control" placeholder="تعليق تجريبي رائع!" value="تعليق تجريبي رائع!">
            </div>
            <div class="form-group">
              <label for="test-comment-username">اسم المستخدم:</label>
              <input type="text" id="test-comment-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <button id="test-comment-btn" class="btn btn-primary" style="width: 100%;">اختبار التعليق</button>
          </div>

          <!-- اختبار الإعجابات -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">❤️</span>
              اختبار الإعجابات
            </h4>
            <div class="form-group">
              <label for="test-like-count">عدد الإعجابات:</label>
              <input type="number" id="test-like-count" class="form-control" min="1" max="1000" value="10">
            </div>
            <div class="form-group">
              <label for="test-like-username">اسم المستخدم:</label>
              <input type="text" id="test-like-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <button id="test-like-btn" class="btn btn-primary" style="width: 100%;">اختبار الإعجاب</button>
          </div>

          <!-- اختبار المتابعة -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">👥</span>
              اختبار المتابعة
            </h4>
            <div class="form-group">
              <label for="test-follow-username">اسم المستخدم:</label>
              <input type="text" id="test-follow-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <button id="test-follow-btn" class="btn btn-primary" style="width: 100%;">اختبار المتابعة</button>
          </div>

          <!-- اختبار المشاركة -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">📤</span>
              اختبار المشاركة
            </h4>
            <div class="form-group">
              <label for="test-share-username">اسم المستخدم:</label>
              <input type="text" id="test-share-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <button id="test-share-btn" class="btn btn-primary" style="width: 100%;">اختبار المشاركة</button>
          </div>

          <!-- اختبار الانضمام -->
          <div class="test-event-card" style="background-color: var(--section-bg); border: 1px solid var(--border-color); border-radius: 10px; padding: 20px; transition: all 0.3s ease;">
            <h4 style="color: var(--text-color); margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
              <span style="font-size: 1.5em;">🚪</span>
              اختبار الانضمام
            </h4>
            <div class="form-group">
              <label for="test-join-username">اسم المستخدم:</label>
              <input type="text" id="test-join-username" class="form-control" placeholder="test_user" value="test_user">
            </div>
            <button id="test-join-btn" class="btn btn-primary" style="width: 100%;">اختبار الانضمام</button>
          </div>

        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: var(--table-header-bg); border-radius: 8px; border: 1px solid var(--border-color);">
          <h5 style="color: var(--text-color); margin-bottom: 10px;">📝 ملاحظات مهمة:</h5>
          <ul style="color: var(--text-secondary); margin: 0; padding-right: 20px;">
            <li>هذه الاختبارات تحاكي الأحداث الحقيقية من TikTok Live</li>
            <li>ستعمل الاختبارات فقط إذا كان لديك تعيينات مضبوطة للأحداث المختلفة</li>
            <li>يمكنك مراقبة النتائج في شاشة العرض (Overlay) أو في وحدة التحكم</li>
            <li>الاختبارات لا تؤثر على الأحداث الحقيقية أو الإحصائيات</li>
          </ul>
        </div>
      </div>

      <!-- مربع حوار إضافة/تعديل الربط -->
      <div class="modal fade" id="mappingModal" tabindex="-1" aria-labelledby="mappingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="mappingModalLabel">إضافة ربط جديد</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="edit-mapping-name">اسم التعيين</label>
                <input type="text" id="edit-mapping-name" class="form-control" placeholder="أدخل اسماً وصفياً لهذا التعيين">
              </div>

              <div class="form-group">
                <label for="event-type-select">نوع الحدث</label>
                <select id="event-type-select" class="form-control">
                  <option value="gift">هدية</option>
                  <option value="like">إعجاب</option>
                  <option value="comment">تعليق</option>
                  <option value="follow">متابعة</option>
                  <option value="share">مشاركة</option>
                  <option value="join">انضمام</option>
                </select>
              </div>

              <!-- قسم الهدايا -->
              <div id="gift-event-section" class="event-type-section">
                <div class="form-group">
                  <label for="gift-dropdown">اختر الهدية</label>
                  <div class="custom-select-container">
                    <div class="select-selected" id="selected-gift">أي هدية</div>
                    <input type="hidden" id="edit-gift" value="any">
                    <div class="select-items select-hide" id="gift-options">
                      <div data-value="any" class="gift-option">أي هدية</div>
                      <!-- الهدايا الأخرى ستضاف هنا ديناميكيًا -->
                    </div>
                  </div>
                  <div id="selected-gift-preview" class="mt-2">
                    <div class="selected-gift-card">
                      <img id="selected-gift-image" src="" alt="" class="gift-preview-img">
                      <div class="selected-gift-info">
                        <div id="selected-gift-name" class="gift-name"></div>
                        <div id="selected-gift-diamonds" class="gift-diamonds"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- قسم الإعجابات -->
              <div id="like-event-section" class="event-type-section" style="display: none;">
                <div class="form-group">
                  <label for="like-count">عدد الإعجابات المطلوب للتفعيل</label>
                  <input type="number" id="like-count" class="form-control" min="1" value="10" placeholder="أدخل عدد الإعجابات">
                  <small class="form-text text-muted">سيتم تفعيل الإجراء عند الوصول إلى هذا العدد من الإعجابات</small>
                </div>
              </div>

              <!-- قسم التعليقات -->
              <div id="comment-event-section" class="event-type-section" style="display: none;">
                <div class="form-group">
                  <label for="comment-text">نص التعليق</label>
                  <input type="text" id="comment-text" class="form-control" placeholder="أدخل نص التعليق الذي سيفعل الإجراء">
                  <small class="form-text text-muted">سيتم تفعيل الإجراء عند كتابة هذا التعليق بالضبط</small>
                </div>
              </div>

              <!-- قسم المتابعة -->
              <div id="follow-event-section" class="event-type-section" style="display: none;">
                <div class="form-group">
                  <div class="alert alert-info">
                    سيتم تفعيل الإجراء عند قيام أي مستخدم بمتابعة البث
                  </div>
                </div>
              </div>

              <!-- قسم المشاركة -->
              <div id="share-event-section" class="event-type-section" style="display: none;">
                <div class="form-group">
                  <div class="alert alert-info">
                    سيتم تفعيل الإجراء عند قيام أي مستخدم بمشاركة البث
                  </div>
                </div>
              </div>

              <!-- قسم الانضمام -->
              <div id="join-event-section" class="event-type-section" style="display: none;">
                <div class="form-group">
                  <label for="join-type">نوع الانضمام</label>
                  <select id="join-type" class="form-control">
                    <option value="any">أي مستخدم</option>
                    <option value="specific">مستخدم محدد</option>
                  </select>
                  <small class="form-text text-muted">ملاحظة: يستخدم هذا الحدث أحداث "join" و "member" و "social" في TikTok لضمان التقاط جميع المستخدمين المنضمين.</small>
                </div>
                <div id="specific-user-section" style="display: none;">
                  <div class="form-group">
                    <label for="specific-username">اسم المستخدم</label>
                    <input type="text" id="specific-username" class="form-control" placeholder="أدخل اسم المستخدم المحدد">
                  </div>
                </div>
                <div class="alert alert-info mt-3">
                  <strong>معلومة:</strong> في بعض الأحيان، قد لا ترسل TikTok إشعارات الانضمام لجميع المستخدمين. لزيادة فرص التقاط الانضمامات، يتم استخدام أحداث متعددة (join, member, social). تم تحسين النظام لتجنب تكرار الإجراءات للمستخدم نفسه.
                </div>
              </div>

              <div class="form-group">
                <label for="edit-action">الإجراءات</label>
                <div class="checkbox-group">
                  <div class="form-check">
                    <input type="checkbox" class="form-check-input action-checkbox" id="action-sound" value="sound">
                    <label class="form-check-label" for="action-sound">تشغيل صوت</label>
                  </div>

                  <div class="form-check">
                    <input type="checkbox" class="form-check-input action-checkbox" id="action-image" value="image">
                    <label class="form-check-label" for="action-image">عرض صورة</label>
                  </div>
                  <div class="form-check">
                    <input type="checkbox" class="form-check-input action-checkbox" id="action-video" value="video">
                    <label class="form-check-label" for="action-video">عرض فيديو</label>
                  </div>


                  <div class="form-check">
                    <input type="checkbox" class="form-check-input action-checkbox" id="action-text" value="text">
                    <label class="form-check-label" for="action-text">عرض نص مخصص</label>
                  </div>

                  <div class="form-check">
                    <input type="checkbox" class="form-check-input action-checkbox" id="action-keypress" value="keypress">
                    <label class="form-check-label" for="action-keypress">محاكاة ضغط مفتاح</label>
                  </div>
                </div>
                  </div>

              <!-- قسم محاكاة ضغط المفتاح -->
              <div class="form-group" id="keypress-section" style="display: none;">
                <label>إعدادات محاكاة ضغط المفاتيح</label>
                <div class="keypress-container" style="background-color: var(--section-bg); border-radius: 10px; padding: 15px; border: 1px solid var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease;">
                  <!-- قائمة المفاتيح المضافة -->
                  <div class="keypress-sequence-container mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <h6 style="color: var(--text-color); transition: color 0.3s ease;">تسلسل المفاتيح</h6>
                      <button type="button" id="add-keypress-btn" class="btn btn-sm btn-primary">إضافة مفتاح</button>
                    </div>
                    <div id="keypress-sequence-list" class="mb-3 border rounded p-2" style="min-height: 50px; max-height: 200px; overflow-y: auto; background-color: var(--section-bg); border-color: var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease;">
                      <div class="text-center p-2" style="color: var(--text-secondary);">لم يتم إضافة مفاتيح بعد. انقر على زر "إضافة مفتاح" لإضافة مفتاح جديد.</div>
                    </div>
                    <button type="button" id="test-keypress-sequence-btn" class="btn btn-sm btn-info">اختبار تسلسل المفاتيح</button>
                  </div>

                  <!-- نموذج إضافة مفتاح جديد -->
                  <div class="keypress-editor border rounded p-3 mb-3" style="background-color: var(--section-bg); border-color: var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease;">
                    <h6 style="color: var(--text-color); transition: color 0.3s ease;">إضافة مفتاح جديد</h6>
                    <div class="row mb-3">
                      <div class="col-md-8">
                        <label for="keypress-select" class="form-label" style="color: var(--text-color); transition: color 0.3s ease;">المفتاح:</label>
                        <select id="keypress-select" class="form-control">
                          <option value="space">مفتاح المسافة</option>
                          <option value="enter">مفتاح الإدخال (Enter)</option>
                          <option value="escape">مفتاح الخروج (Escape)</option>
                          <option value="tab">مفتاح التبويب (Tab)</option>
                          <option value="arrowup">السهم لأعلى</option>
                          <option value="arrowdown">السهم لأسفل</option>
                          <option value="arrowleft">السهم لليسار</option>
                          <option value="arrowright">السهم لليمين</option>
                          <option value="backspace">مفتاح المسح (Backspace)</option>
                          <option value="delete">مفتاح الحذف (Delete)</option>
                          <option value="control">مفتاح التحكم (Ctrl)</option>
                          <option value="alt">مفتاح Alt</option>
                          <option value="shift">مفتاح Shift</option>
                          <option value="capslock">مفتاح Caps Lock</option>
                          <option value="home">مفتاح Home</option>
                          <option value="end">مفتاح End</option>
                          <option value="pageup">مفتاح Page Up</option>
                          <option value="pagedown">مفتاح Page Down</option>
                          <option value="f1">F1</option>
                          <option value="f2">F2</option>
                          <option value="f3">F3</option>
                          <option value="f4">F4</option>
                          <option value="f5">F5</option>
                          <option value="f6">F6</option>
                          <option value="f7">F7</option>
                          <option value="f8">F8</option>
                          <option value="f9">F9</option>
                          <option value="f10">F10</option>
                          <option value="f11">F11</option>
                          <option value="f12">F12</option>
                        </select>
                      </div>
                      <div class="col-md-4">
                        <label for="keypress-delay" class="form-label" style="color: var(--text-color); transition: color 0.3s ease;">تأخير (مللي ثانية):</label>
                        <input type="number" id="keypress-delay" class="form-control" value="0" min="0" max="10000" step="100">
                      </div>
                    </div>

                    <div class="custom-key-container mb-3">
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="custom-key-checkbox">
                        <label class="form-check-label" for="custom-key-checkbox">إدخال مفتاح مخصص</label>
                      </div>
                      <input type="text" id="custom-key-input" class="form-control mt-2" placeholder="اكتب المفتاح المخصص هنا (يمكن إدخال مفاتيح متعددة)" style="display: none;">
                      <small class="form-text mt-1" id="custom-key-help" style="display: none; color: var(--text-secondary);">
                        يمكنك إدخال مفتاح واحد أو عدة مفاتيح مفصولة بمسافات أو فواصل (مثال: a b c أو a,b,c)
                      </small>
                    </div>

                    <div class="keypress-modifiers mb-3">
                      <label class="form-label" style="color: var(--text-color); transition: color 0.3s ease;">مفاتيح التعديل:</label>
                      <div class="d-flex gap-3">
                        <div class="form-check">
                          <input type="checkbox" class="form-check-input modifier-checkbox" id="modifier-ctrl">
                          <label class="form-check-label" for="modifier-ctrl">Ctrl</label>
                        </div>
                        <div class="form-check">
                          <input type="checkbox" class="form-check-input modifier-checkbox" id="modifier-alt">
                          <label class="form-check-label" for="modifier-alt">Alt</label>
                        </div>
                        <div class="form-check">
                          <input type="checkbox" class="form-check-input modifier-checkbox" id="modifier-shift">
                          <label class="form-check-label" for="modifier-shift">Shift</label>
                        </div>
                      </div>
                    </div>

                    <div class="keypress-hold-options mb-3">
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="hold-key-checkbox">
                        <label class="form-check-label" for="hold-key-checkbox">ضغط مستمر (Hold)</label>
                      </div>
                      <div id="hold-duration-container" style="display: none; margin-top: 10px;">
                        <label for="hold-duration" class="form-label" style="color: var(--text-color); transition: color 0.3s ease;">مدة الضغط (مللي ثانية):</label>
                        <input type="number" id="hold-duration" class="form-control" value="1000" min="100" max="60000" step="100">
                        <small class="form-text text-muted">المدة التي سيتم فيها الاستمرار بالضغط على المفتاح قبل تحريره.</small>
                      </div>
                    </div>

                    <div class="key-preview mb-3 p-2 border rounded" style="background-color: var(--table-header-bg); border-color: var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease;">
                      <span style="color: var(--text-color); transition: color 0.3s ease;">معاينة المفتاح: <span id="key-preview-text">Space</span></span>
                    </div>

                    <button type="button" id="add-key-to-sequence-btn" class="btn btn-sm btn-success">إضافة للتسلسل</button>
                    <button type="button" id="test-single-key-btn" class="btn btn-sm btn-info">اختبار المفتاح</button>
                  </div>
                </div>
              </div>

              <div class="form-group" id="edit-sound-group">
                <label for="edit-sound-file">ملف الصوت</label>
                <div class="file-upload sound-upload">
                  <label for="edit-sound-file" style="display: block; width: 100%; height: 100%; cursor: pointer;">
                    انقر لاختيار ملف صوت من جهازك
                  </label>
                  <input type="file" id="edit-sound-file" accept="audio/*" style="display: none;">
                </div>
                <div id="sound-preview"></div>
                  </div>

              <div class="form-group" id="file-upload-section" style="display: none;">
                <label>ملف الوسائط (صورة أو فيديو)</label>
                <div class="file-upload">
                  <label for="edit-file" style="display: block; width: 100%; height: 100%; cursor: pointer;">
                    انقر أو اسحب ملف هنا للرفع
                  </label>
                  <input type="file" id="edit-file" accept="image/*,video/*,audio/*" style="display: none;">
                      </div>
                <div id="file-preview"></div>
                      </div>

              <div class="form-group" id="custom-text-section" style="display: none;">
                <label for="custom-text">النص المخصص</label>
                <textarea id="custom-text" class="form-control" rows="3" placeholder="أدخل النص المخصص الذي تريد عرضه"></textarea>
                <small class="form-text text-muted">سيتم عرض هذا النص في الـ overlay عند تنفيذ الإجراء</small>
              </div>

              <div class="form-group">
                <label for="edit-duration">مدة العرض (ثوان)</label>
                <div class="duration-input">
                  <input type="number" id="edit-duration" min="1" max="30" value="5" class="form-control">
                    </div>
                  </div>

              <div class="form-group">
                <label for="overlay-selector">شاشة العرض المستهدفة</label>
                <div>
                  <select id="overlay-selector" class="form-control">
                    <option value="default">الافتراضية</option>
                    <option value="1">شاشة العرض 1</option>
                    <option value="2">شاشة العرض 2</option>
                    <option value="3">شاشة العرض 3</option>
                    <option value="4">شاشة العرض 4</option>
                    <option value="5">شاشة العرض 5</option>
                    <option value="6">شاشة العرض 6</option>
                    <option value="7">شاشة العرض 7</option>
                    <option value="8">شاشة العرض 8</option>
                    <option value="9">شاشة العرض 9</option>
                    <option value="10">شاشة العرض 10</option>
                    <option value="11">شاشة العرض 11</option>
                    <option value="12">شاشة العرض 12</option>
                    <option value="13">شاشة العرض 13</option>
                    <option value="14">شاشة العرض 14</option>
                    <option value="15">شاشة العرض 15</option>
                    <option value="16">شاشة العرض 16</option>
                    <option value="17">شاشة العرض 17</option>
                    <option value="18">شاشة العرض 18</option>
                    <option value="19">شاشة العرض 19</option>
                    <option value="20">شاشة العرض 20</option>
                  </select>
                  <div class="overlay-link-display mt-2">
                    <a href="/overlay.html?id=default" id="overlay-link" target="_blank" class="btn btn-sm btn-secondary">
                      فتح رابط شاشة العرض: <span id="overlay-link-text">/overlay.html?id=default</span>
                    </a>
                  </div>
                </div>
              </div>

              <div class="form-group" id="sound-playback-options">
                <div class="form-check">
                  <input type="checkbox" class="form-check-input" id="play-sound-on-server">
                  <label class="form-check-label" for="play-sound-on-server">تشغيل الصوت على الخادم (بدون الاعتماد على شاشة العرض)</label>
                  <small class="form-text text-muted d-block mt-1">
                    ملاحظة: يتطلب هذا الخيار أن يكون لديك مكبرات صوت متصلة بالخادم. يعمل بشكل أفضل مع ملفات WAV و MP3.
                  </small>
                </div>
              </div>

              <div class="test-action" style="background-color: var(--table-header-bg); border-radius: 10px; padding: 15px; margin-top: 20px; border: 1px solid var(--border-color); transition: background-color 0.3s ease, border-color 0.3s ease;">
                <h4 style="color: var(--text-color); transition: color 0.3s ease;">اختبار الإجراء</h4>
                <p style="color: var(--text-secondary); transition: color 0.3s ease;">اختبر هذا الإجراء قبل حفظه للتأكد من أنه يعمل كما هو متوقع.</p>
                <button id="test-action-btn" class="btn btn-info">اختبار الإجراء</button>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" id="save-mapping-btn" class="btn btn-primary">حفظ</button>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        StreamTok &copy; 2025
        - By : Abdelrahman Mohamed
      </div>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/settings-manager.js"></script>
  <script>
    // اتصال Socket.IO
    const socket = io({
      reconnectionAttempts: 5
    });

    // جعل socket متاح عالمي<|im_start|>
    window.socket = socket;

    // إرسال session عند الاتصال إذا كان المستخدم مسجل دخول
    socket.on('connect', () => {
      console.log('🔌 Socket connected to mappings');

      // إرسال session إذا كان هناك مستخدم مسجل دخول
      if (window.pendingUserAuth) {
        socket.emit('userAuthenticated', window.pendingUserAuth);
        console.log('✅ Pending user session sent to server:', window.pendingUserAuth.email);
        window.pendingUserAuth = null;
      } else if (window.firebaseHelpers && window.firebaseHelpers.getCurrentUser()) {
        const user = window.firebaseHelpers.getCurrentUser();
        if (user && user.emailVerified) {
          socket.emit('userAuthenticated', {
            userId: user.uid,
            email: user.email,
            emailVerified: user.emailVerified
          });
          console.log('✅ Current user session sent to server:', user.email);
        }
      }
    });

    // تهيئة نظام إدارة الإعدادات
    SettingsManager.initialize().then(() => {
      // الحصول على تعيينات الهدايا من الإعدادات
      const giftMappingsSettings = SettingsManager.get('giftMappings');
      if (giftMappingsSettings && giftMappingsSettings.mappings) {
        // تحديث المتغيرات المحلية
        giftMappings = giftMappingsSettings.mappings;
      }

      // طلب قائمة الهدايا المتاحة أولاً
      socket.emit('getAvailableGifts');
    });

    let giftMappings = [];
    let availableGifts = [];
    let currentEditingId = null;
    let currentEditingFilePath = null;
    let currentMediaFilePath = null;
    let currentSoundFilePath = null;

    // مصفوفة تخزين تسلسل المفاتيح
    let keypressSequence = [];

    // متغير لتخزين مرجع نافذة overlay المفتوحة
    let overlayWindow = null;

    // عند تحميل الصفحة
    $(document).ready(function() {
      // التأكد من أن القائمة المنسدلة مخفية عند بدء التشغيل
      const giftOptions = document.getElementById('gift-options');
      if (giftOptions) {
        giftOptions.classList.add('select-hide');
        giftOptions.style.display = 'none';
      }

      // إضافة معالج لتغيير شاشة العرض المستهدفة
      $('#overlay-selector').on('change', function() {
        const selectedOverlayId = $(this).val();
        const overlayUrl = `/overlay.html?id=${selectedOverlayId}`;

        // تحديث الرابط والنص
        $('#overlay-link').attr('href', overlayUrl);
        $('#overlay-link-text').text(overlayUrl);

        console.log(`تم تحديث شاشة العرض المستهدفة إلى: ${selectedOverlayId}`);
      });

      // إضافة معالج لتغيير نوع الحدث
      $('#event-type-select').on('change', function() {
        const eventType = $(this).val();
        // إخفاء جميع أقسام الأحداث
        $('.event-type-section').hide();
        // إظهار القسم المناسب للحدث المختار
        $(`#${eventType}-event-section`).show();

        // معالجة خاصة لقسم الانضمام
        if (eventType === 'join') {
          // إضافة معالج لتغيير نوع الانضمام
          $('#join-type').on('change', function() {
            const joinType = $(this).val();
            if (joinType === 'specific') {
              $('#specific-user-section').show();
            } else {
              $('#specific-user-section').hide();
            }
          });

          // تشغيل التغيير الأولي
          $('#join-type').trigger('change');
        }
      });

      // تشغيل التغيير الأولي لنوع الحدث
      $('#event-type-select').trigger('change');

      // تحديث جدول التعيينات بعد تحميل الصفحة
      setTimeout(function() {
        socket.emit('getGiftMappings');
      }, 500);
      // تعيين معالج حدث النقر على زر اختبار الإجراء
      $('#test-action-btn').on('click', function() {
        testAction(); // استدعاء دالة الاختبار بدون تمرير تعيين
      });

      // استقبال رسائل الخادم
      socket.on('serverMessage', function(data) {
        if (data.type === 'success') {
          showToast(data.message);
        } else if (data.type === 'error') {
          showAlert(data.message, 'error');
        } else {
          showToast(data.message);
        }
      });

      // استقبال نتيجة اختبار المفاتيح
      socket.on('keypressTestResult', function(data) {
        if (data.success) {
          if (data.completed) {
            showAlert('تم تنفيذ اختبار المفاتيح بنجاح', 'success');
          } else {
            console.log('اختبار المفاتيح:', data);
            showAlert(data.message, 'info');
          }
        } else {
          showAlert('فشل اختبار المفاتيح: ' + (data.message || 'خطأ غير معروف'), 'error');
        }
      });

      // معالج زر تحديث قائمة الهدايا
      $('#updateGiftsBtn').on('click', function() {
        socket.emit('getAvailableGifts');
        socket.emit('getGiftMappings');
        showAlert('جاري تحديث البيانات...', 'info');
      });

      // الحصول على قائمة الهدايا المتاحة
      console.log('طلب قائمة الهدايا المتاحة...');
      socket.emit('getAvailableGifts');

      // طلب بيانات الهدايا مباشرة من API TikTok
      console.log('طلب تحديث قائمة الهدايا من API TikTok...');
      socket.emit('updateAvailableGifts');

      // تحديث قائمة الهدايا كل 5 دقائق
      setInterval(() => {
        console.log('تحديث دوري لقائمة الهدايا...');
        socket.emit('getAvailableGifts');
      }, 5 * 60 * 1000);

      // الحصول على تعيينات الهدايا الحالية
      console.log('طلب تعيينات الهدايا الحالية...');
      socket.emit('getGiftMappings');

      // إضافة معالج للاستجابة لتحديث الهدايا
      socket.on('giftsUpdated', function(response) {
        console.log('تم تحديث قائمة الهدايا:', response);
        // طلب الهدايا المحدثة
        socket.emit('getAvailableGifts');
      });

      // إضافة معالجات أحداث تغيير الملفات
      document.getElementById('edit-sound-file').addEventListener('change', function() {
        handleSoundFileUpload();
      });

      document.getElementById('edit-file').addEventListener('change', function() {
        handleMediaFileUpload();
      });

      // معالج زر إضافة تعيين جديد
      $('#add-mapping-btn').click(function() {
        // التحقق من حد التعيينات للمستخدمين المجانيين
        if (!checkMappingLimit()) {
          return; // إيقاف العملية إذا تم تجاوز الحد
        }

        // إعادة ضبط متغيرات التحرير الحالية
        currentEditingId = null;
        currentMediaFilePath = null;
        currentSoundFilePath = null;
        currentEditingFilePath = null;

        // إعادة ضبط نوع الحدث
        $('#event-type-select').val('gift');

        // إخفاء جميع أقسام الأحداث ثم إظهار قسم الهدايا
        $('.event-type-section').hide();
        $('#gift-event-section').show();

        // إعادة ضبط حقول الأحداث المختلفة
        $('#like-count').val('10');
        $('#comment-text').val('');
        $('#join-type').val('any');
        $('#specific-username').val('');
        $('#specific-user-section').hide();

        // إعادة ضبط نموذج الإدخال للهدايا
        $('#edit-gift').val('any');
        $('#selected-gift').html('أي هدية');
        $('#selected-gift-preview').hide();

        // إزالة التحديد من جميع الخيارات
        document.querySelectorAll('.gift-option').forEach(item => {
          item.classList.remove('selected');
        });

        // تحديد خيار "أي هدية"
        const anyOption = document.querySelector('.gift-option[data-value="any"]');
        if (anyOption) {
          anyOption.classList.add('selected');
        }

        // إعادة ضبط الإجراءات
        $('.action-checkbox').prop('checked', false);
        $('#action-sound').prop('checked', true);

        // إعادة ضبط خيار تشغيل الصوت على الخادم
        $('#play-sound-on-server').prop('checked', false);

        // إعادة ضبط النموذج
        $('#edit-duration').val('5');
        $('#overlay-selector').val('default');
        $('#edit-file').val('');
        $('#file-preview').empty();
        $('#edit-sound-file').val('');
        $('#sound-preview').empty();
        $('#custom-key-checkbox').prop('checked', false);
        $('#custom-key-input').hide().val('');
        $('#keypress-select').prop('disabled', false);
        $('.modifier-checkbox').prop('checked', false);

        // تحديث أقسام تحميل الملفات
        updateFileUploadSections();

        // تحديث رابط الشاشة المستهدفة
        const overlayUrl = '/overlay.html?id=default';
        $('#overlay-link').attr('href', overlayUrl);
        $('#overlay-link-text').text(overlayUrl);

        // تحديث مكتبة الهدايا
        if (availableGifts.length > 0) {
          updateGiftLibrary(availableGifts);

          // إزالة التحديد من جميع الخيارات
          document.querySelectorAll('.gift-option').forEach(item => {
            item.classList.remove('selected');
          });
        } else {
          socket.emit('getAvailableGifts');
        }

        // تغيير عنوان النافذة المنبثقة
        $('#mappingModalLabel').text('إضافة ربط جديد');

        // إظهار النافذة المنبثقة
        new bootstrap.Modal(document.getElementById('mappingModal')).show();

        console.log('تم إعادة ضبط النموذج لإضافة تعيين جديد');
      });

      // إعداد القائمة المنسدلة للهدايا
      setupGiftSelector();

      // إعداد معالجات أحداث محاكاة ضغط المفاتيح
      setupKeypressHandlers();

      // تحديث عناصر تحميل الملفات حسب الإجراءات المحددة
      updateFileUploadSections();

      // إعداد تحديث دوري للبيانات كل 5 دقائق
      setInterval(function() {
        socket.emit('getGiftMappings');
      }, 300000); // 5 دقائق = 300,000 مللي ثانية

      // إضافة عنصر الشعار الصامت في أسفل يمين الصفحة
      if (!document.getElementById('toast-container')) {
        const toast = document.createElement('div');
        toast.id = 'toast-container';
        toast.style.position = 'fixed';
        toast.style.bottom = '30px';
        toast.style.right = '30px';
        toast.style.zIndex = '9999';
        toast.style.minWidth = '220px';
        toast.style.maxWidth = '350px';
        toast.style.background = 'rgba(40,40,40,0.95)';
        toast.style.color = '#fff';
        toast.style.borderRadius = '10px';
        toast.style.padding = '16px 24px';
        toast.style.fontSize = '16px';
        toast.style.boxShadow = '0 4px 16px rgba(0,0,0,0.18)';
        toast.style.display = 'none';
        toast.style.transition = 'opacity 0.4s';
        document.body.appendChild(toast);
      }
    });

    // دالة لعرض الشعار الصامت
    function showToast(message) {
      const toast = document.getElementById('toast-container');
      if (!toast) return;
      toast.textContent = message;
      toast.style.display = 'block';
      toast.style.opacity = '1';
      setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => { toast.style.display = 'none'; }, 400);
      }, 2500);
    }

    // عرض رسالة تنبيه للمستخدم
    function showAlert(message, type = 'error') {
      const alertBox = $('#alertBox');
      alertBox.removeClass().addClass('alert');

      if (type === 'success') {
        alertBox.addClass('success');
      } else if (type === 'info') {
        alertBox.css({
          'background-color': '#cce5ff',
          'color': '#004085'
        });
      }

      alertBox.html(message).slideDown();

      // إخفاء الرسالة بعد 3 ثوانٍ
      setTimeout(function() {
        alertBox.slideUp();
      }, 3000);
    }

    // إعداد القائمة المنسدلة للهدايا
    function setupGiftSelector() {
      const selectedGift = document.getElementById('selected-gift');
      const giftOptions = document.getElementById('gift-options');
      const hiddenInput = document.getElementById('edit-gift');
      const giftPreview = document.getElementById('selected-gift-preview');
      const giftImage = document.getElementById('selected-gift-image');
      const giftName = document.getElementById('selected-gift-name');
      const giftDiamonds = document.getElementById('selected-gift-diamonds');

      if (!selectedGift || !giftOptions || !hiddenInput) {
        console.error('لم يتم العثور على عناصر اختيار الهدية');
        return;
      }

      // طلب قائمة الهدايا من الخادم فور تحميل الصفحة
      socket.emit('getAvailableGifts');

      // إظهار/إخفاء قائمة الخيارات
      selectedGift.addEventListener('click', function(e) {
        e.stopPropagation();

        // إذا كانت القائمة مخفية
        if (giftOptions.classList.contains('select-hide')) {
          // إظهار القائمة
          giftOptions.classList.remove('select-hide');
          giftOptions.style.display = 'block';
          this.classList.add('select-arrow-active');
        } else {
          // إخفاء القائمة
          giftOptions.classList.add('select-hide');
          giftOptions.style.display = 'none';
          this.classList.remove('select-arrow-active');
        }
      });

      // إضافة مستمع للنقر على خيارات الهدايا
      document.addEventListener('click', function(e) {
        if (e.target.classList.contains('gift-option')) {
          const value = e.target.getAttribute('data-value');
          hiddenInput.value = value;
          selectedGift.innerHTML = e.target.innerHTML;
          giftOptions.classList.add('select-hide');
          selectedGift.classList.remove('select-arrow-active');

          // إذا كانت القيمة المختارة هي "أي هدية"
          if (value === 'any') {
            giftPreview.style.display = 'none';
            return;
          }

          // البحث عن الهدية المختارة في قائمة الهدايا المتاحة
          const selectedGiftObj = availableGifts.find(gift => {
            const giftId = gift.id || gift.ID || gift.Id || '';
            return giftId === value;
          });

          if (selectedGiftObj) {
            // عرض معلومات الهدية المختارة
            const giftId = selectedGiftObj.id || selectedGiftObj.ID || selectedGiftObj.Id || '';
            const name = selectedGiftObj.name || selectedGiftObj.Name || selectedGiftObj.describe || giftId;
            const diamondCount = selectedGiftObj.diamondCost || selectedGiftObj.diamondCount || selectedGiftObj.diamond_count || 0;

            // التعامل مع صورة الهدية
            let imageUrl = '';
            if (selectedGiftObj.imageUrl) {
              imageUrl = selectedGiftObj.imageUrl;
            } else if (selectedGiftObj.image && selectedGiftObj.image.url_list && selectedGiftObj.image.url_list.length > 0) {
              imageUrl = selectedGiftObj.image.url_list[0];
            } else if (selectedGiftObj.icon && selectedGiftObj.icon.url_list && selectedGiftObj.icon.url_list.length > 0) {
              imageUrl = selectedGiftObj.icon.url_list[0];
            }

            // تحديث عناصر العرض
            giftImage.src = imageUrl || '/placeholder.png';
            giftImage.alt = name;
            giftName.textContent = name;
            giftDiamonds.textContent = diamondCount ? `${diamondCount} 🪙` : '';

            // إظهار بطاقة الهدية
            giftPreview.style.display = 'block';
          } else {
            // إخفاء بطاقة الهدية إذا لم يتم العثور على الهدية
            giftPreview.style.display = 'none';
          }
        } else if (e.target !== selectedGift) {
          giftOptions.classList.add('select-hide');
          giftOptions.style.display = 'none';
          selectedGift.classList.remove('select-arrow-active');
        }
      });

      // تحديث قائمة الهدايا عند استلام البيانات
      socket.on('availableGifts', function(data) {
        console.log('تم استلام الهدايا المتاحة');

        // معالجة البيانات المستلمة بشكل صحيح
        if (Array.isArray(data)) {
          // البيانات هي مصفوفة مباشرة (التنسيق الجديد)
          availableGifts = data;
          console.log(`تم استلام ${data.length} هدية`);
          showToast(`تم تحميل ${data.length} هدية بنجاح`);
        } else if (data && data.gifts && Array.isArray(data.gifts)) {
          // البيانات مغلفة بمفتاح gifts (التنسيق القديم)
          availableGifts = data.gifts;
          console.log(`تم استلام ${data.gifts.length} هدية`);
          showToast(`تم تحميل ${data.gifts.length} هدية بنجاح`);
        } else {
          console.error('تنسيق بيانات الهدايا غير متوقع:', data);
          availableGifts = [];
          showToast('لم يتم العثور على هدايا');
        }

        console.log(`تم تحميل ${availableGifts.length} هدية`);

        if (availableGifts.length > 0) {
          // عرض بعض الأمثلة للتصحيح
          console.log('أمثلة للهدايا:', availableGifts.slice(0, 3));
        }

        // تحديث مكتبة الهدايا
        updateGiftLibrary(availableGifts);

        // الآن بعد تحميل الهدايا، قم بتحديث جدول التعيينات
        if (giftMappings.length > 0) {
          // تحديث الجدول بعد تحميل الهدايا
          updateMappingsTable();
        } else {
          // إذا لم تكن هناك تعيينات محلية، اطلبها من الخادم
          socket.emit('getGiftMappings');
        }
      });
    }

    // تحديث عرض مكتبة الهدايا
    function updateGiftLibrary(gifts) {
      const giftOptions = document.getElementById('gift-options');
      const hiddenInput = document.getElementById('edit-gift');
      const selectedGift = document.getElementById('selected-gift');

      if (!giftOptions || !hiddenInput || !selectedGift) {
        console.error('لم يتم العثور على عناصر اختيار الهدية');
        return;
      }

      console.log('تحديث مكتبة الهدايا مع', gifts ? gifts.length : 0, 'هدية');

      // الاحتفاظ بالقيمة الحالية المحددة
      const currentValue = hiddenInput.value;

      // مسح المحتوى الحالي باستثناء خيار "أي هدية"
      const anyGiftOption = giftOptions.querySelector('[data-value="any"]');
      giftOptions.innerHTML = '';

      // التأكد من أن القائمة مخفية عند بدء التحديث
      giftOptions.classList.add('select-hide');
      giftOptions.style.display = 'none';

      // إعادة إضافة خيار "أي هدية"
      if (anyGiftOption) {
        giftOptions.appendChild(anyGiftOption);
      } else {
        const anyOption = document.createElement('div');
        anyOption.className = 'gift-option';
        anyOption.setAttribute('data-value', 'any');
        anyOption.textContent = 'أي هدية';
        giftOptions.appendChild(anyOption);
      }

      // التحقق من وجود هدايا
      if (!Array.isArray(gifts) || gifts.length === 0) {
        const noGifts = document.createElement('div');
        noGifts.className = 'gift-option';
        noGifts.textContent = 'لم يتم العثور على هدايا. حاول تحديث قائمة الهدايا.';
        giftOptions.appendChild(noGifts);
        return;
      }

      try {
        // فرز الهدايا حسب عدد الماسات (من الأصغر للأكبر - تصاعدي)
        console.log('فرز الهدايا تصاعدياً...');
        const sortedGifts = [...gifts].sort((a, b) => {
          // استخدام diamondCost أو diamondCount اعتمادًا على ما هو متاح
          const diamondA = a.diamondCost || a.diamondCount || a.diamond_count || 0;
          const diamondB = b.diamondCost || b.diamondCount || b.diamond_count || 0;
          return diamondA - diamondB; // ترتيب تصاعدي
        });

        // إضافة الهدايا إلى القائمة المنسدلة
        let addedCount = 0;
        sortedGifts.forEach(gift => {
          try {
            // التعامل مع معرف الهدية - قد يكون في id أو ID
            const giftId = gift.id || gift.ID || gift.Id || '';
            if (!giftId) {
              console.error('الهدية لا تحتوي على معرف:', gift);
              return;
            }

            // التعامل مع اسم الهدية - قد يكون في name أو Name أو describe
            const giftName = gift.name || gift.Name || gift.describe || giftId;

            // استخدام diamondCost أو diamondCount أو diamond_count اعتمادًا على ما هو متاح
            const diamondCount = gift.diamondCost || gift.diamondCount || gift.diamond_count || 0;

            // التعامل مع صورة الهدية - قد تكون في imageUrl أو في مكان آخر
            let imageUrl = '';
            if (gift.imageUrl) {
              imageUrl = gift.imageUrl;
            } else if (gift.image && gift.image.url_list && gift.image.url_list.length > 0) {
              imageUrl = gift.image.url_list[0];
            } else if (gift.icon && gift.icon.url_list && gift.icon.url_list.length > 0) {
              imageUrl = gift.icon.url_list[0];
            }

            const diamondText = diamondCount ? ` (${diamondCount} 🪙)` : '';

            // إنشاء خيار جديد
            const giftOption = document.createElement('div');
            giftOption.className = 'gift-option';
            giftOption.setAttribute('data-value', giftId);
            giftOption.setAttribute('data-name', giftName);
            giftOption.setAttribute('data-diamonds', diamondCount);

            // إضافة صورة الهدية والنص
            giftOption.innerHTML = `
              ${imageUrl ? `<img src="${imageUrl}" alt="${giftName}" class="gift-icon" onerror="this.style.display='none'">` : ''}
              <span>${giftName}${diamondText}</span>
            `;

            giftOptions.appendChild(giftOption);
            addedCount++;
          } catch (giftError) {
            console.error('خطأ في إضافة هدية:', giftError);
          }
        });

        console.log(`تمت إضافة ${addedCount} هدية إلى القائمة المنسدلة`);
        showToast(`تم تحميل ${addedCount} هدية بنجاح`);

        // إذا كانت هناك قيمة محددة مسبقًا، نحاول العثور عليها وتحديدها
        if (currentValue && currentValue !== 'any') {
          const selectedOption = giftOptions.querySelector(`[data-value="${currentValue}"]`);
          if (selectedOption) {
            // محاكاة النقر على الخيار المحدد
            selectedOption.click();
          }
        }
      } catch (error) {
        console.error('خطأ في تحديث مكتبة الهدايا:', error);
      }
    }

    // اختيار هدية من المكتبة
    function selectGiftFromLibrary(gift) {
      console.log('تم اختيار الهدية:', gift);

      // الحصول على عناصر الواجهة
      const selectedGift = document.getElementById('selected-gift');
      const hiddenInput = document.getElementById('edit-gift');
      const giftPreview = document.getElementById('selected-gift-preview');
      const giftImage = document.getElementById('selected-gift-image');
      const giftName = document.getElementById('selected-gift-name');
      const giftDiamonds = document.getElementById('selected-gift-diamonds');

      if (!selectedGift || !hiddenInput) {
        console.error('لم يتم العثور على عناصر اختيار الهدية');
        return;
      }

      // تحديث قيمة القائمة المنسدلة
      hiddenInput.value = gift.id;

      // التعامل مع صورة الهدية
      let imageUrl = gift.imageUrl || '';

      // استخدام diamondCost أو diamondCount اعتمادًا على ما هو متاح
      const diamondCount = gift.diamondCost || gift.diamondCount || 0;
      const diamondText = diamondCount ? ` (${diamondCount} 🪙)` : '';

      // تحديث العنصر المحدد
      selectedGift.innerHTML = `
        ${imageUrl ? `<img src="${imageUrl}" alt="${gift.name}" class="gift-icon" onerror="this.style.display='none'">` : ''}
        <span>${gift.name || gift.id}${diamondText}</span>
      `;

      // تحديث بطاقة الهدية المختارة
      if (giftPreview && giftImage && giftName && giftDiamonds) {
        // تحديث عناصر العرض
        giftImage.src = imageUrl || '/placeholder.png';
        giftImage.alt = gift.name || gift.id;
        giftName.textContent = gift.name || gift.id;
        giftDiamonds.textContent = diamondCount ? `${diamondCount} 🪙` : '';

        // إظهار بطاقة الهدية
        giftPreview.style.display = 'block';
      }
    }



    // تحديث أقسام تحميل الملفات بناءً على الإجراءات المحددة
    function updateFileUploadSections() {
      // التحقق من وجود إجراءات محددة
      const hasImageAction = $('#action-image').is(':checked');
      const hasVideoAction = $('#action-video').is(':checked');
      const hasSoundAction = $('#action-sound').is(':checked');
      const hasKeypressAction = $('#action-keypress').is(':checked');
      const hasTextAction = $('#action-text').is(':checked');

      // إظهار أو إخفاء قسم ملف الوسائط (للصور والفيديو)
      if (hasImageAction || hasVideoAction) {
        $('#file-upload-section').show();
      } else {
        $('#file-upload-section').hide();
      }

      // إظهار أو إخفاء قسم ملف الصوت
      if (hasSoundAction) {
        $('#edit-sound-group').show();
      } else {
        $('#edit-sound-group').hide();
      }

      // إظهار أو إخفاء قسم النص المخصص
      if (hasTextAction) {
        $('#custom-text-section').show();
      } else {
        $('#custom-text-section').hide();
      }

      // إظهار أو إخفاء قسم محاكاة ضغط المفتاح
      if (hasKeypressAction) {
        $('#keypress-section').show();
      } else {
        $('#keypress-section').hide();
      }
    }

    // إضافة مستمع للنقر على صناديق الاختيار الخاصة بالإجراءات
    $(document).on('change', '.action-checkbox', function() {
      // التأكد من تحديد إجراء واحد على الأقل
      if ($('.action-checkbox:checked').length === 0) {
        alert('يجب تحديد إجراء واحد على الأقل');
        $(this).prop('checked', true);
        return;
      }
      updateFileUploadSections();
    });

    // تحرير تعيين موجود
    function editMapping(id) {
      try {
        console.log('تحرير التعيين:', id);

        // التحقق من وجود التعيين قبل المتابعة
        // تحويل المعرفات إلى نصوص للمقارنة
        const mappingIndex = giftMappings.findIndex(m => String(m.id) === String(id));
        if (mappingIndex === -1) {
          console.error('لم يتم العثور على التعيين:', id);
          console.log('التعيينات المتاحة:', giftMappings.map(m => m.id));
          showAlert('لم يتم العثور على التعيين المطلوب. قد يكون قد تم حذفه مسبقًا. سيتم تحديث القائمة الآن.');
          socket.emit('getGiftMappings'); // تحديث القائمة
          return;
        }

        const mapping = giftMappings[mappingIndex];
        currentEditingId = String(id);
        console.log('تم تعيين معرف التعيين الحالي للتعديل:', currentEditingId);

        // إعادة ضبط الإجراءات
        $('.action-checkbox').prop('checked', false);

        // تعيين نوع الحدث
        const eventType = mapping.eventType || 'gift'; // استخدام 'gift' كقيمة افتراضية للتوافق مع البيانات القديمة
        $('#event-type-select').val(eventType);

        // إخفاء جميع أقسام الأحداث ثم إظهار القسم المناسب
        $('.event-type-section').hide();
        $(`#${eventType}-event-section`).show();

        // تعبئة البيانات حسب نوع الحدث
        switch (eventType) {
          case 'gift':
            // تعيين الهدية المختارة
            const selectedGift = document.getElementById('selected-gift');
            const giftOptions = document.getElementById('gift-options');
            const hiddenInput = document.getElementById('edit-gift');
            const giftPreview = document.getElementById('selected-gift-preview');
            const giftImage = document.getElementById('selected-gift-image');
            const giftName = document.getElementById('selected-gift-name');
            const giftDiamonds = document.getElementById('selected-gift-diamonds');

            if (!selectedGift || !hiddenInput) {
              console.error('لم يتم العثور على عناصر اختيار الهدية');
              return;
            }

            // البحث عن معلومات الهدية
            if (mapping.giftId === 'any') {
              // حالة "أي هدية"
              hiddenInput.value = 'any';
              selectedGift.innerHTML = 'أي هدية';
              giftPreview.style.display = 'none';

              // إزالة التحديد من جميع الخيارات
              document.querySelectorAll('.gift-option').forEach(item => {
                item.classList.remove('selected');
              });

              // تحديد خيار "أي هدية"
              const anyOption = giftOptions.querySelector('[data-value="any"]');
              if (anyOption) {
                anyOption.classList.add('selected');
              }
            } else {
              // البحث عن الهدية في قائمة الهدايا المتاحة
              const giftInfo = availableGifts.find(g => {
                const giftId = g.id || g.ID || g.Id || '';
                return giftId === mapping.giftId;
              });

              // تحديث قيمة القائمة المنسدلة
              hiddenInput.value = mapping.giftId;

              if (giftInfo) {
                // استخدام diamondCost أو diamondCount اعتمادًا على ما هو متاح
                const diamondCount = giftInfo.diamondCost || giftInfo.diamondCount || giftInfo.diamond_count || 0;

                // التعامل مع صورة الهدية
                let imageUrl = '';
                if (giftInfo.imageUrl) {
                  imageUrl = giftInfo.imageUrl;
                } else if (giftInfo.image && giftInfo.image.url_list && giftInfo.image.url_list.length > 0) {
                  imageUrl = giftInfo.image.url_list[0];
                } else if (giftInfo.icon && giftInfo.icon.url_list && giftInfo.icon.url_list.length > 0) {
                  imageUrl = giftInfo.icon.url_list[0];
                }

                const giftNameText = giftInfo.name || giftInfo.Name || giftInfo.describe || giftInfo.id;
                const diamondText = diamondCount ? ` (${diamondCount} 🪙)` : '';

                // تحديث العنصر المحدد
                selectedGift.innerHTML = `
                  ${imageUrl ? `<img src="${imageUrl}" alt="${giftNameText}" class="gift-icon" onerror="this.style.display='none'">` : ''}
                  <span>${giftNameText}${diamondText}</span>
                `;

                // تحديث بطاقة الهدية المختارة
                if (giftPreview && giftImage && giftName && giftDiamonds) {
                  // تحديث عناصر العرض
                  giftImage.src = imageUrl || '/placeholder.png';
                  giftImage.alt = giftNameText;
                  giftName.textContent = giftNameText;
                  giftDiamonds.textContent = diamondCount ? `${diamondCount} 🪙` : '';

                  // إظهار بطاقة الهدية
                  giftPreview.style.display = 'block';
                }

                // إزالة التحديد من جميع الخيارات
                document.querySelectorAll('.gift-option').forEach(item => {
                  item.classList.remove('selected');
                });

                // تحديد الخيار المناسب
                const selectedOption = giftOptions.querySelector(`[data-value="${mapping.giftId}"]`);
                if (selectedOption) {
                  selectedOption.classList.add('selected');
                }

                console.log('تم العثور على معلومات الهدية:', giftInfo);
              } else {
                // إذا لم يتم العثور على معلومات الهدية
                selectedGift.textContent = mapping.giftId || 'هدية غير معروفة';
                giftPreview.style.display = 'none';
                console.log('لم يتم العثور على معلومات الهدية، استخدام المعرف:', mapping.giftId);

                // طلب قائمة الهدايا المحدثة
                socket.emit('getAvailableGifts');
              }
            }
            break;

          case 'like':
            // تعبئة بيانات الإعجابات
            $('#like-count').val(mapping.likeCount || 10);
            break;

          case 'comment':
            // تعبئة بيانات التعليقات
            $('#comment-text').val(mapping.commentText || '');
            break;

          case 'follow':
            // لا توجد بيانات إضافية للمتابعة
            break;

          case 'share':
            // لا توجد بيانات إضافية للمشاركة
            break;

          case 'join':
            // تعبئة بيانات الانضمام
            $('#join-type').val(mapping.joinType || 'any');

            if (mapping.joinType === 'specific') {
              $('#specific-user-section').show();
              $('#specific-username').val(mapping.specificUsername || '');
            } else {
              $('#specific-user-section').hide();
            }
            break;
        }

        // تعيين باقي القيم
        $('#edit-mapping-name').val(mapping.mappingName || '');
        $('#edit-duration').val(mapping.duration || 5);
        $('#overlay-selector').val(mapping.overlayId || 'default');

        // تحديث رابط الشاشة المستهدفة
        const overlayUrl = `/overlay.html?id=${mapping.overlayId || 'default'}`;
        $('#overlay-link').attr('href', overlayUrl);
        $('#overlay-link-text').text(overlayUrl);

        // حفظ مسارات الملفات الحالية
        currentMediaFilePath = mapping.mediaFilePath;
        currentSoundFilePath = mapping.soundFilePath;

        // تحديد الإجراءات المختارة
        if (mapping.actions && Array.isArray(mapping.actions)) {
          mapping.actions.forEach(action => {
            $(`#action-${action}`).prop('checked', true);
          });

          // تحميل النص المخصص إذا كان موجوداً
          if (mapping.actions.includes('text') && mapping.customText) {
            $('#custom-text').val(mapping.customText);
          }
        } else if (mapping.action) {
          // للتوافق مع البيانات القديمة
          $(`#action-${mapping.action}`).prop('checked', true);
        }

        // إعادة ضبط وتحميل تسلسل المفاتيح
        keypressSequence = [];
        if (mapping.keypressSequence && Array.isArray(mapping.keypressSequence)) {
          keypressSequence = [...mapping.keypressSequence];
          updateKeypressSequenceDisplay();
        } else {
          updateKeypressSequenceDisplay();
        }

        // تحديث أقسام تحميل الملفات
        updateFileUploadSections();

        // تحميل خيار تشغيل الصوت على الخادم
        $('#play-sound-on-server').prop('checked', mapping.playSoundOnServer === true);

        // عرض معلومات الملف الحالي إذا وجد
        if (mapping.mediaFilePath) {
          const currentFileText = window.TranslationSystem ? window.TranslationSystem.translateText('الملف الحالي:') : 'الملف الحالي:';
          $('#file-preview').html(`<div>${currentFileText} ${mapping.mediaFilePath.split('/').pop()}</div>`);
        } else {
          $('#file-preview').empty();
        }

        // عرض معلومات ملف الصوت الحالي إذا وجد
        if (mapping.soundFilePath) {
          const currentFileText = window.TranslationSystem ? window.TranslationSystem.translateText('الملف الحالي:') : 'الملف الحالي:';
          $('#sound-preview').html(`<div>${currentFileText} ${mapping.soundFilePath.split('/').pop()}</div>`);
        } else {
          $('#sound-preview').empty();
        }

        // إعادة ضبط حقول الملفات
        $('#edit-file').val('');
        $('#edit-sound-file').val('');

        // تحديث مكتبة الهدايا وتحديد الهدية الحالية
        if (availableGifts.length > 0) {
          updateGiftLibrary(availableGifts);

          // تحديد الهدية في القائمة
          setTimeout(() => {
            if (mapping.giftId !== 'any') {
              const giftOption = document.querySelector(`.gift-option[data-value="${mapping.giftId}"]`);
              if (giftOption) {
                // إزالة التحديد من جميع الخيارات
                document.querySelectorAll('.gift-option').forEach(item => {
                  item.classList.remove('selected');
                });

                // تحديد الهدية
                giftOption.classList.add('selected');
              }
            }
          }, 100);
        } else {
          socket.emit('getAvailableGifts');
        }

        // تغيير عنوان النافذة المنبثقة
        $('#mappingModalLabel').text('تعديل الربط');

        // إظهار النافذة المنبثقة
        new bootstrap.Modal(document.getElementById('mappingModal')).show();
      } catch (error) {
        console.error('خطأ في تحرير التعيين:', error);
        alert('حدث خطأ أثناء تحرير التعيين: ' + error.message);
      }
    }

    // حذف تعيين
    function deleteMapping(id) {
      try {
        // التحقق من وجود التعيين قبل المتابعة
        // تحويل المعرفات إلى نصوص للمقارنة
        const mappingIndex = giftMappings.findIndex(m => String(m.id) === String(id));
        if (mappingIndex === -1) {
          console.error('لم يتم العثور على التعيين للحذف:', id);
          console.log('التعيينات المتاحة:', giftMappings.map(m => m.id));
          showAlert('لم يتم العثور على التعيين المطلوب. قد يكون قد تم حذفه مسبقًا. سيتم تحديث القائمة الآن.');
          socket.emit('getGiftMappings'); // تحديث القائمة
          return;
        }

        if (confirm('هل أنت متأكد من حذف هذا التعيين؟')) {
          console.log('جاري حذف التعيين رقم:', id, 'نوع المعرف:', typeof id);

          // حذف التعيين من الإعدادات المحلية أولاً
          try {
            const currentMappings = SettingsManager.get('giftMappings.mappings') || [];
            const localIndex = currentMappings.findIndex(m => String(m.id) === String(id));

            if (localIndex !== -1) {
              currentMappings.splice(localIndex, 1);
              // حفظ التعيينات محلياً مباشرة
              SettingsManager.settings.giftMappings = { mappings: currentMappings };
              SettingsManager.saveToLocalStorage();
            }
          } catch (error) {
            console.error('خطأ في حذف التعيين من الإعدادات المحلية:', error);
          }

          // حذف التعيين مؤقتًا من القائمة المحلية قبل استجابة الخادم
          giftMappings.splice(mappingIndex, 1);
          updateMappingsTable();

          // تحديث مؤشر الحد فوراً
          setTimeout(updateMappingLimitUI, 50);

          // إرسال المعرف كنص للتأكد من التطابق
          socket.emit('deleteGiftMapping', { id: String(id) });

          // معالج استجابة الحذف الناجح
          socket.once('mappingDeleted', function(response) {
            console.log('تم حذف التعيين بنجاح:', response);
            showAlert('تم حذف التعيين بنجاح', 'success');

            // تحديث قائمة التعيينات
            socket.emit('getGiftMappings');

            // تحديث مؤشر الحد
            setTimeout(updateMappingLimitUI, 100);
          });

          // معالج خطأ الحذف
          socket.once('errorMessage', function(error) {
            console.error('خطأ في حذف التعيين:', error);
            // إعادة تحميل القائمة بغض النظر عن الخطأ
            socket.emit('getGiftMappings');

            // عرض رسالة الخطأ
            showAlert('خطأ في حذف التعيين: ' + error.message);
          });
        }
      } catch (error) {
        console.error('خطأ في حذف التعيين:', error);
        showAlert('حدث خطأ أثناء حذف التعيين: ' + error.message);
        socket.emit('getGiftMappings'); // تحديث القائمة على أي حال
      }
    }

    // وظيفة لاختبار الإجراء
    function testAction(mapping) {
      try {
        console.log('جاري اختبار الإجراء:', mapping);

        // جمع الإجراءات المختارة
        let actions = [];
        if (mapping && mapping.actions && Array.isArray(mapping.actions) && mapping.actions.length > 0) {
          actions = mapping.actions;
        } else if (mapping && mapping.action) {
          actions = [mapping.action];
        } else {
          $('.action-checkbox:checked').each(function() {
            actions.push($(this).val());
          });
        }

        if (actions.length === 0) {
          alert('الرجاء اختيار إجراء واحد على الأقل');
          return;
        }

        // جمع البيانات الأخرى
        const duration = mapping ? (mapping.duration || 5) : parseInt($('#edit-duration').val() || 5, 10);
        const overlayId = mapping ? (mapping.overlayId || 'default') : ($('#overlay-selector').val() || 'default');

        // الحصول على نوع الحدث
        const eventType = mapping ? (mapping.eventType || 'gift') : $('#event-type-select').val();

        console.log('مدة العرض التي سيتم إرسالها:', duration);
        console.log('نوع الحدث:', eventType);

        // تحضير بيانات الاختبار
        const testData = {
          type: actions.includes('sound') ? 'sound' : (actions.includes('image') ? 'image' : (actions.includes('video') ? 'video' : 'alert')),
          actions: actions,
          duration: duration,
          overlayId: overlayId,
          eventType: eventType,
          testMode: true,
          playSoundOnServer: mapping ? mapping.playSoundOnServer : $('#play-sound-on-server').is(':checked'), // إضافة خيار تشغيل الصوت على الخادم
          customText: mapping && mapping.customText ? mapping.customText : (actions.includes('text') ? $('#custom-text').val() : '') // إضافة النص المخصص
        };

        // إضافة البيانات الخاصة بكل نوع حدث
        switch (eventType) {
          case 'gift':
            const giftId = mapping ? (mapping.giftId || 'any') : $('#edit-gift').val();
            testData.giftId = giftId;
            break;

          case 'like':
            const likeCount = mapping ? (mapping.likeCount || 10) : parseInt($('#like-count').val() || 10, 10);
            testData.likeCount = likeCount;
            break;

          case 'comment':
            const commentText = mapping ? (mapping.commentText || '') : $('#comment-text').val();
            testData.commentText = commentText;
            break;

          case 'follow':
            // لا توجد بيانات إضافية للمتابعة
            break;

          case 'share':
            // لا توجد بيانات إضافية للمشاركة
            break;

          case 'join':
            const joinType = mapping ? (mapping.joinType || 'any') : $('#join-type').val();
            testData.joinType = joinType;

            if (joinType === 'specific') {
              const specificUsername = mapping ? (mapping.specificUsername || '') : $('#specific-username').val();
              testData.specificUsername = specificUsername;
            }
            break;
        }

        // إضافة معلومات محاكاة المفاتيح إذا كان الإجراء هو محاكاة مفتاح
        if (actions.includes('keypress')) {
          if (mapping && mapping.keypressSequence && Array.isArray(mapping.keypressSequence)) {
            // استخدام تسلسل المفاتيح من التعيين الموجود
            testData.keypressSequence = mapping.keypressSequence;
          } else if (keypressSequence.length > 0) {
            // استخدام تسلسل المفاتيح الحالي
            testData.keypressSequence = keypressSequence;
          } else {
            // إذا لم يكن هناك تسلسل، استخدم المفتاح الحالي
            const currentKey = getKeypressInfo();
            testData.keypressSequence = [{
              ...currentKey,
              delay: parseInt($('#keypress-delay').val(), 10) || 0
            }];
          }
        }

        // معالجة مسارات الملفات الموجودة
        if (mapping && mapping.mediaFilePath) {
          testData.filePath = mapping.mediaFilePath;
        } else if (currentMediaFilePath) {
          testData.filePath = currentMediaFilePath;
        }
        if (mapping && mapping.soundFilePath) {
          testData.soundFile = mapping.soundFilePath;
        } else if (currentSoundFilePath) {
          testData.soundFile = currentSoundFilePath;
        }

        // إذا كان المستخدم قد اختار ملفات جديدة ولم يحفظها بعد
        const mediaFileInput = document.getElementById('edit-file');
        const soundFileInput = document.getElementById('edit-sound-file');
        let filePromises = [];

        // رفع ملف الوسائط إذا تم اختياره ولم يكن مرفوعًا بالفعل
        if ((actions.includes('image') || actions.includes('video')) &&
            mediaFileInput && mediaFileInput.files && mediaFileInput.files[0] && !testData.filePath) {
          const filePromise = new Promise((resolve) => {
            const file = mediaFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                fileType: file.type
              });

              socket.once('fileUploaded', function(response) {
                if (response.success) {
                  testData.filePath = response.filePath;
                  console.log('تم رفع ملف الوسائط للاختبار:', response.filePath);
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        }

        // رفع ملف الصوت إذا تم اختياره
        if (actions.includes('sound') && soundFileInput && soundFileInput.files && soundFileInput.files[0] && !testData.soundFile) {
          const filePromise = new Promise((resolve) => {
            const file = soundFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                fileType: file.type
              });

              socket.once('fileUploaded', function(response) {
                if (response.success) {
                  testData.soundFile = response.filePath;
                  console.log('تم رفع ملف الصوت للاختبار:', response.filePath);
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        }

        // إرسال الإجراء للاختبار بعد الانتهاء من رفع الملفات
        Promise.all(filePromises).then(() => {
          // تنفيذ اختبار المفاتيح إذا كان موجوداً
          if (actions.includes('keypress') && testData.keypressSequence && testData.keypressSequence.length > 0) {
            console.log('🧪 اختبار تسلسل المفاتيح:', testData.keypressSequence);
            socket.emit('testKeypressSequence', { sequence: testData.keypressSequence });
          }

          // إرسال الإجراء للاختبار
          socket.emit('joinOverlayRoom', { overlayId: testData.overlayId || 'default' });
          console.log('إرسال بيانات الاختبار:', testData);
          socket.emit('testAction', testData);
          showToast('تم إرسال الإجراء للاختبار بنجاح.');
        }).catch(error => {
          console.error('خطأ أثناء رفع الملفات للاختبار:', error);
          showToast('حدث خطأ أثناء رفع الملفات للاختبار: ' + error.message);
        });
      } catch (error) {
        console.error('خطأ في اختبار الإجراء:', error);
        showToast('حدث خطأ أثناء اختبار الإجراء: ' + error.message);
      }
    }

    // معالجة رفع ملف الوسائط
    function handleMediaFileUpload() {
      const fileInput = document.getElementById('edit-file');
      const previewContainer = document.getElementById('file-preview');

      if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];

        // عرض معاينة للملف
        previewContainer.innerHTML = '';

        // التحقق من نوع الملف
        if (file.type.startsWith('image/')) {
          // إذا كان الملف صورة
          const img = document.createElement('img');
          img.src = URL.createObjectURL(file);
          img.style.maxWidth = '100%';
          img.style.maxHeight = '150px';
          previewContainer.appendChild(img);
        } else if (file.type.startsWith('video/')) {
          // إذا كان الملف فيديو
          const video = document.createElement('video');
          video.src = URL.createObjectURL(file);
          video.controls = true;
          video.autoplay = false;
          video.style.maxWidth = '100%';
          video.style.maxHeight = '150px';
          previewContainer.appendChild(video);
        } else {
          // أنواع ملفات أخرى
          previewContainer.textContent = `تم اختيار: ${file.name}`;
        }

        // إضافة اسم الملف
        const fileNameEl = document.createElement('div');
        fileNameEl.textContent = file.name;
        fileNameEl.style.marginTop = '5px';
        fileNameEl.style.fontSize = '14px';
        previewContainer.appendChild(fileNameEl);

        // إضافة مؤشر تحميل
        const loadingEl = document.createElement('div');
        loadingEl.textContent = 'جاري تحميل الملف...';
        loadingEl.style.marginTop = '5px';
        loadingEl.style.fontSize = '14px';
        loadingEl.style.color = '#666';
        previewContainer.appendChild(loadingEl);

        // رفع الملف مباشرة إلى الخادم عند اختياره
        const reader = new FileReader();
        reader.onload = function(e) {
          const uploadId = 'media_' + Date.now();
          socket.emit('uploadFile', {
            fileName: file.name,
            fileContent: e.target.result,
            uploadId: uploadId
          });

          socket.once('fileUploaded', function(response) {
            if (response.uploadId !== uploadId) return; // تجاهل إذا لم يكن للوسائط
            if (response.success) {
              // تحديث مؤشر التحميل
              loadingEl.textContent = 'تم تحميل الملف بنجاح';
              loadingEl.style.color = 'green';

              // تخزين مسار الملف للاستخدام لاحقًا
              currentMediaFilePath = response.filePath;
              console.log('تم رفع الملف مباشرة، المسار:', currentMediaFilePath);

              // إضافة زر لفتح الملف في نافذة جديدة
              const viewBtn = document.createElement('a');
              viewBtn.textContent = 'عرض الملف';
              viewBtn.href = currentMediaFilePath;
              viewBtn.target = '_blank';
              viewBtn.className = 'btn btn-sm btn-info mt-2 me-2';
              previewContainer.appendChild(viewBtn);
            } else {
              loadingEl.textContent = 'فشل تحميل الملف: ' + (response.error || 'خطأ غير معروف');
              loadingEl.style.color = 'red';
            }
          });
        };
        reader.readAsDataURL(file);

        // إضافة زر لحذف الملف المختار
        const removeBtn = document.createElement('button');
        removeBtn.textContent = 'إلغاء';
        removeBtn.className = 'btn btn-sm btn-danger mt-2';
        // استخدام معالج حدث مباشر بدلاً من jQuery
        removeBtn.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          fileInput.value = '';
          previewContainer.innerHTML = '';
          currentMediaFilePath = null; // إلغاء مسار الملف المخزن
        });
        previewContainer.appendChild(removeBtn);
      }
    }

    // معالجة رفع ملف الصوت
    function handleSoundFileUpload() {
      const fileInput = document.getElementById('edit-sound-file');
      const previewContainer = document.getElementById('sound-preview');

      if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];

        // عرض معاينة للملف
        previewContainer.innerHTML = '';

        // إنشاء عنصر صوت للمعاينة
        const audio = document.createElement('audio');
        audio.src = URL.createObjectURL(file);
        audio.controls = true;
        previewContainer.appendChild(audio);

        // إضافة اسم الملف
        const fileNameEl = document.createElement('div');
        fileNameEl.textContent = file.name;
        fileNameEl.style.marginTop = '5px';
        fileNameEl.style.fontSize = '14px';
        previewContainer.appendChild(fileNameEl);

        // إضافة مؤشر تحميل
        const loadingEl = document.createElement('div');
        loadingEl.textContent = 'جاري تحميل الملف...';
        loadingEl.style.marginTop = '5px';
        loadingEl.style.fontSize = '14px';
        loadingEl.style.color = '#666';
        previewContainer.appendChild(loadingEl);

        // رفع الملف مباشرة إلى الخادم عند اختياره
        const reader = new FileReader();
        reader.onload = function(e) {
          const uploadId = 'sound_' + Date.now();
          socket.emit('uploadFile', {
            fileName: file.name,
            fileContent: e.target.result,
            uploadId: uploadId
          });

          socket.once('fileUploaded', function(response) {
            if (response.uploadId !== uploadId) return; // تجاهل إذا لم يكن للصوت
            if (response.success) {
              // تحديث مؤشر التحميل
              loadingEl.textContent = 'تم تحميل الملف بنجاح';
              loadingEl.style.color = 'green';

              // تخزين مسار الملف للاستخدام لاحقًا
              currentSoundFilePath = response.filePath;
              console.log('تم رفع ملف الصوت مباشرة، المسار:', currentSoundFilePath);

              // إضافة زر لفتح الملف في نافذة جديدة
              const viewBtn = document.createElement('a');
              viewBtn.textContent = 'تشغيل الصوت';
              viewBtn.href = currentSoundFilePath;
              viewBtn.target = '_blank';
              viewBtn.className = 'btn btn-sm btn-info mt-2 me-2';
              previewContainer.appendChild(viewBtn);
            } else {
              loadingEl.textContent = 'فشل تحميل الملف: ' + (response.error || 'خطأ غير معروف');
              loadingEl.style.color = 'red';
            }
          });
        };
        reader.readAsDataURL(file);

        // إضافة زر لحذف الملف المختار
        const removeBtn = document.createElement('button');
        removeBtn.textContent = 'إلغاء';
        removeBtn.className = 'btn btn-sm btn-danger mt-2';
        // استخدام معالج حدث مباشر بدلاً من jQuery
        removeBtn.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          fileInput.value = '';
          previewContainer.innerHTML = '';
          currentSoundFilePath = null; // إلغاء مسار الملف المخزن
        });
        previewContainer.appendChild(removeBtn);
      }
    }

    // تعديل وظيفة حفظ التعديلات
    function saveMapping() {
      try {
        // التحقق من حد التعيينات للمستخدمين المجانيين (فقط للتعيينات الجديدة)
        if (!currentEditingId && !checkMappingLimit()) {
          return; // إيقاف العملية إذا تم تجاوز الحد
        }

        // جمع الإجراءات المختارة
        let actions = [];
        $('.action-checkbox:checked').each(function() {
          actions.push($(this).val());
        });

        if (actions.length === 0) {
          showAlert('الرجاء اختيار إجراء واحد على الأقل');
          return;
        }

        // التحقق من خيار تشغيل الصوت على الخادم
        if ($('#play-sound-on-server').is(':checked') && !actions.includes('sound')) {
          showAlert('يجب تحديد إجراء "تشغيل صوت" عند اختيار خيار تشغيل الصوت على الخادم');
          return;
        }

        // جمع البيانات من نموذج التعديل
        const giftId = $('#edit-gift').val();
        const duration = parseInt($('#edit-duration').val(), 10);
        const overlayId = $('#overlay-selector').val();

        // التحقق من صحة البيانات
        if (!giftId) {
          showAlert('يرجى اختيار هدية');
          return;
        }

        // الحصول على معلومات الهدية
        let giftName = '', giftImage = '';
        const selectedGift = availableGifts.find(g => {
          const giftId2 = g.id || g.ID || g.Id || '';
          return String(giftId2) === String(giftId);
        });

        if (selectedGift) {
          giftName = selectedGift.name || selectedGift.Name || selectedGift.describe || selectedGift.id;
          console.log(`تم العثور على الهدية: ${giftName} للمعرف: ${giftId}`);

          // التعامل مع صورة الهدية
          if (selectedGift.imageUrl) {
            giftImage = selectedGift.imageUrl;
          } else if (selectedGift.image && selectedGift.image.url_list && selectedGift.image.url_list.length > 0) {
            giftImage = selectedGift.image.url_list[0];
          } else if (selectedGift.icon && selectedGift.icon.url_list && selectedGift.icon.url_list.length > 0) {
            giftImage = selectedGift.icon.url_list[0];
          }
        } else if (giftId === 'any') {
          giftName = 'أي هدية';
        } else {
          // محاولة البحث مرة أخرى بطريقة أكثر مرونة
          const foundGift = availableGifts.find(g => String(g.id || g.ID || g.Id || '') === String(giftId));
          if (foundGift) {
            giftName = foundGift.name || foundGift.Name || foundGift.describe || foundGift.id;
            console.log(`تم العثور على الهدية بطريقة بديلة: ${giftName} للمعرف: ${giftId}`);

            // التعامل مع صورة الهدية
            if (foundGift.imageUrl) {
              giftImage = foundGift.imageUrl;
            } else if (foundGift.image && foundGift.image.url_list && foundGift.image.url_list.length > 0) {
              giftImage = foundGift.image.url_list[0];
            } else if (foundGift.icon && foundGift.icon.url_list && foundGift.icon.url_list.length > 0) {
              giftImage = foundGift.icon.url_list[0];
            }
          } else {
            console.log(`لم يتم العثور على الهدية للمعرف: ${giftId}`);
          }
        }

        // تجهيز بيانات التعيين
        const mappingData = {
          id: currentEditingId || null,  // إعطاء قيمة null عند الإنشاء، والسماح للخادم بإنشاء معرف جديد
          giftId: giftId,
          giftName: giftName,
          giftImage: giftImage,
          condition: 'any', // تعيين الشرط على 'any' دائماً
          actions: actions,
          duration: duration,
          overlayId: overlayId,
          playSoundOnServer: $('#play-sound-on-server').is(':checked'), // إضافة خيار تشغيل الصوت على الخادم
          customText: actions.includes('text') ? $('#custom-text').val() : '' // إضافة النص المخصص إذا كان الإجراء يتضمن عرض نص
        };

        // إضافة تسلسل المفاتيح إذا كان إجراء محاكاة المفاتيح محدداً
        if (actions.includes('keypress') && keypressSequence.length > 0) {
          mappingData.keypressSequence = keypressSequence;
        }

        console.log('محاولة حفظ التعيين برقم:', mappingData.id, 'نوع المعرف:', typeof mappingData.id);

        // معالجة الملفات
        const mediaFileInput = document.getElementById('edit-file');
        const soundFileInput = document.getElementById('edit-sound-file');
        let filePromises = [];

        // رفع ملف الوسائط إذا تم اختياره
        if ((actions.includes('image') || actions.includes('video') || actions.includes('custom')) &&
            mediaFileInput && mediaFileInput.files && mediaFileInput.files[0]) {
          const filePromise = new Promise((resolve) => {
            const file = mediaFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              const uploadId = 'save_media_' + Date.now();
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                uploadId: uploadId
              });

              socket.once('fileUploaded', function(response) {
                if (response.uploadId !== uploadId) return;
                if (response.success) {
                  mappingData.mediaFilePath = response.filePath;
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        } else if (currentMediaFilePath) {
          mappingData.mediaFilePath = currentMediaFilePath;
        }

        // رفع ملف الصوت إذا تم اختياره
        if (actions.includes('sound') && soundFileInput && soundFileInput.files && soundFileInput.files[0]) {
          const filePromise = new Promise((resolve) => {
            const file = soundFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              const uploadId = 'save_sound_' + Date.now();
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                uploadId: uploadId
              });

              socket.once('fileUploaded', function(response) {
                if (response.uploadId !== uploadId) return;
                if (response.success) {
                  mappingData.soundFilePath = response.filePath;
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        } else if (currentSoundFilePath) {
          mappingData.soundFilePath = currentSoundFilePath;
        }

        // إرسال البيانات بعد رفع الملفات
        Promise.all(filePromises).then(() => {
          console.log('إرسال بيانات التعيين:', mappingData);

          // إرسال بيانات التعيين إلى الخادم
          socket.emit('saveGiftMapping', mappingData);

          // معالج استجابة الخادم للحفظ
          socket.once('mappingSaved', function(response) {
            console.log('تم حفظ التعيين بنجاح:', response);
            showAlert('تم حفظ التعيين بنجاح', 'success');

            // تحديث قائمة التعيينات
            socket.emit('getGiftMappings');

            // إغلاق النافذة المنبثقة
            document.getElementById('mappingModal').querySelector('.btn-close').click();
          });

          // معالج خطأ الحفظ
          socket.once('errorMessage', function(error) {
            console.error('خطأ في حفظ التعيين:', error);

            // إذا كان الخطأ أن التعيين غير موجود، فربما تم حذفه من قبل
            if (error.message === 'التعيين غير موجود' && currentEditingId) {
              showAlert('لا يمكن تعديل هذا التعيين لأنه ربما تم حذفه. سيتم إنشاء تعيين جديد.');
              // حاول إنشاء تعيين جديد بدلًا من التعديل
              currentEditingId = null;
              mappingData.id = null;  // تأكد من إلغاء المعرف السابق

              // ارسل الطلب مرة أخرى، لكن بدون معرف (كتعيين جديد)
              console.log('إعادة محاولة حفظ التعيين كتعيين جديد');
              socket.emit('saveGiftMapping', mappingData);

              // إضافة معالج للإستجابة مرة أخرى
              socket.once('mappingSaved', function(response) {
                console.log('تم حفظ التعيين الجديد بنجاح:', response);
                showAlert('تم حفظ التعيين كجديد بنجاح', 'success');

                // تحديث قائمة التعيينات
                socket.emit('getGiftMappings');

                // إغلاق النافذة المنبثقة
                document.getElementById('mappingModal').querySelector('.btn-close').click();
              });

              return;
            }

            showAlert('حدث خطأ أثناء حفظ التعيين: ' + error.message);
          });
        }).catch(error => {
          console.error('خطأ في رفع الملفات:', error);
          showAlert('حدث خطأ أثناء رفع الملفات: ' + error.message);
        });
      } catch (error) {
        console.error('خطأ في حفظ التعيين:', error);
        showAlert('حدث خطأ أثناء حفظ التعيين: ' + error.message);
      }
    }

    // دالة للحصول على اسم العرض للإجراء
    function getActionDisplayName(action) {
      switch(action) {
        case 'image':
          return window.TranslationSystem ? window.TranslationSystem.translateText('عرض صورة') : 'عرض صورة';
        case 'video':
          return window.TranslationSystem ? window.TranslationSystem.translateText('تشغيل فيديو') : 'تشغيل فيديو';
        case 'audio':
        case 'sound':
          return window.TranslationSystem ? window.TranslationSystem.translateText('تشغيل صوت') : 'تشغيل صوت';
        case 'confetti':
          return window.TranslationSystem ? window.TranslationSystem.translateText('عرض كونفيتي') : 'عرض كونفيتي';
        case 'alert':
          return window.TranslationSystem ? window.TranslationSystem.translateText('عرض تنبيه') : 'عرض تنبيه';
        case 'text':
          return window.TranslationSystem ? window.TranslationSystem.translateText('عرض نص') : 'عرض نص';
        case 'both':
          return window.TranslationSystem ? window.TranslationSystem.translateText('صوت + تنبيه') : 'صوت + تنبيه';
        case 'custom':
          return window.TranslationSystem ? window.TranslationSystem.translateText('إجراء مخصص') : 'إجراء مخصص';
        case 'keypress':
          return window.TranslationSystem ? window.TranslationSystem.translateText('محاكاة مفتاح') : 'محاكاة مفتاح';
        default:
          return action || (window.TranslationSystem ? window.TranslationSystem.translateText('غير معروف') : 'غير معروف');
      }
    }

    // معالج حفظ التعديلات
    $('#save-mapping-btn').click(function() {
      console.log('زر الحفظ تم النقر عليه. معرف التعيين الحالي:', currentEditingId);
      saveMapping();
    });

    // تحديث واجهة المستخدم عند استلام تعيينات الهدايا
    socket.on('giftMappings', function(data) {
      console.log('تم استلام تعيينات الهدايا:', data);

      // التأكد من تنسيق البيانات الصحيح
      if (Array.isArray(data)) {
        giftMappings = data;
      } else if (data && data.mappings && Array.isArray(data.mappings)) {
        giftMappings = data.mappings;
      } else {
        console.error('تنسيق بيانات التعيينات غير صالح:', data);
        giftMappings = [];
      }

      updateMappingsTable();
    });

    // تحديث واجهة المستخدم عند استلام تحديث لتعيينات الهدايا
    socket.on('giftMappingsUpdated', function(data) {
      console.log('تم استلام تحديث لتعيينات الهدايا:', data);

      // التأكد من تنسيق البيانات الصحيح
      if (data && data.mappings && Array.isArray(data.mappings)) {
        giftMappings = data.mappings;
        updateMappingsTable();


      }
    });

    // استقبال نتيجة اختبار المفاتيح
    socket.on('keypressTestResult', function(data) {
      if (data.success) {
        showAlert(data.message, 'success');
      } else {
        showAlert(data.message || 'حدث خطأ أثناء اختبار المفاتيح', 'error');
      }
    });

    // تحديث واجهة المستخدم بعد حفظ التعيين
    socket.on('mappingSaved', function(response) {
      console.log('تم حفظ التعيين بنجاح:', response);

      // لا نقوم بإعادة تحميل التعيينات هنا لأننا نتوقع أن نتلقى حدث giftMappingsUpdated


    });

    // تحديث واجهة المستخدم بعد حذف التعيين
    socket.on('mappingDeleted', function(response) {
      console.log('تم حذف التعيين بنجاح:', response);

      // لا نقوم بإعادة تحميل التعيينات هنا لأننا نتوقع أن نتلقى حدث giftMappingsUpdated
    });

    // تحديث جدول التعيينات
    function updateMappingsTable() {
      try {
        const tableBody = $('#mappings-table-body');
        if (!tableBody.length) {
          console.error('لم يتم العثور على جدول التعيينات');
          return;
        }

        tableBody.empty();

        if (!Array.isArray(giftMappings) || giftMappings.length === 0) {
          tableBody.append(`<tr><td colspan="5" class="text-center">${window.TranslationSystem ? window.TranslationSystem.translateText('لا توجد تعيينات حتى الآن') : 'لا توجد تعيينات حتى الآن'}</td></tr>`);
          return;
        }

        giftMappings.forEach((mapping, index) => {
          // تحديد ما إذا كان التعيين نشط أم معطل
          const isActive = isMappingActive(index);
          // معالجة الإجراءات المتعددة
          const actions = mapping.actions || [];
          let actionText = '';

          if (actions.length === 0 && mapping.action) {
            // للتوافق مع البيانات القديمة
            actionText = getActionDisplayName(mapping.action);
          } else {
            // للتعيينات الجديدة متعددة الإجراءات
            actionText = actions.map(action => getActionDisplayName(action)).join(' + ');
          }

          // استخدام اسم التعيين إذا كان متوفراً
          const mappingName = mapping.mappingName || (window.TranslationSystem ? window.TranslationSystem.translateText('تعيين بدون اسم') : 'تعيين بدون اسم');

          // تحديد نوع الحدث وتفاصيله
          let eventTypeText = '';
          let eventDetailsText = '';

          // التعامل مع أنواع الأحداث المختلفة
          const eventType = mapping.eventType || 'gift'; // استخدام 'gift' كقيمة افتراضية للتوافق مع البيانات القديمة

          switch (eventType) {
            case 'gift':
              eventTypeText = `<span class="badge bg-danger">${window.TranslationSystem ? window.TranslationSystem.translateText('هدية') : 'هدية'}</span>`;

              // البحث عن معلومات الهدية من القائمة المتاحة
              // تحسين البحث عن الهدية باستخدام مقارنة النصوص
              const gift = Array.isArray(availableGifts) ?
                availableGifts.find(g => {
                  const giftId = g.id || g.ID || g.Id || '';
                  return String(giftId) === String(mapping.giftId);
                }) : null;

              // طباعة معلومات تصحيح للتحقق من البحث
              console.log(`البحث عن الهدية بمعرف: ${mapping.giftId}، تم العثور: ${gift ? 'نعم' : 'لا'}`);

              let giftName, giftImage, diamondCount;

              if (gift) {
                // التعامل مع اسم الهدية
                giftName = gift.name || gift.Name || gift.describe || gift.id;

                // التعامل مع صورة الهدية
                if (gift.imageUrl) {
                  giftImage = gift.imageUrl;
                } else if (gift.image && gift.image.url_list && gift.image.url_list.length > 0) {
                  giftImage = gift.image.url_list[0];
                } else if (gift.icon && gift.icon.url_list && gift.icon.url_list.length > 0) {
                  giftImage = gift.icon.url_list[0];
                } else {
                  giftImage = '/placeholder.png';
                }

                // التحقق من صحة الرابط
                if (giftImage && !giftImage.startsWith('http') && !giftImage.startsWith('/')) {
                  giftImage = 'https://' + giftImage;
                }

                // التعامل مع عدد الماسات
                const diamonds = gift.diamondCost || gift.diamondCount || gift.diamond_count || 0;
                diamondCount = diamonds ? `(${diamonds} 🪙)` : '';

                eventDetailsText = `
                  <div class="gift-type-container horizontal">
                    <img src="${giftImage}" alt="${giftName}" class="gift-icon" onerror="this.style.display='none'">
                    <div class="gift-info">
                      <span class="gift-type-name">${giftName}</span>
                      <span class="gift-type-subname">${diamondCount}</span>
                    </div>
                  </div>
                `;
              } else if (mapping.giftId === 'any') {
                eventDetailsText = `
                  <div class="gift-type-container horizontal">
                    <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('أي هدية') : 'أي هدية'}</span>
                  </div>
                `;
              } else {
                // محاولة إضافية للبحث عن الهدية في قائمة الهدايا المتاحة
                // البحث بطريقة أكثر مرونة
                let foundGift = null;
                if (Array.isArray(availableGifts) && availableGifts.length > 0) {
                  // محاولة البحث مرة أخرى بطريقة أكثر مرونة
                  foundGift = availableGifts.find(g => String(g.id || g.ID || g.Id || '') === String(mapping.giftId));
                }

                // استخدام المعلومات المخزنة في التعيين أو من الهدية التي تم العثور عليها
                giftName = foundGift ? (foundGift.name || foundGift.Name || foundGift.describe) : (mapping.giftName || (window.TranslationSystem ? window.TranslationSystem.translateText('هدية غير معروفة') : 'هدية غير معروفة'));
                giftImage = foundGift ? (foundGift.imageUrl || (foundGift.image && foundGift.image.url_list && foundGift.image.url_list[0])) : (mapping.giftImage || '/placeholder.png');

                // طباعة معلومات تصحيح
                console.log(`استخدام اسم الهدية: ${giftName} للمعرف: ${mapping.giftId}`);

                eventDetailsText = `
                  <div class="gift-type-container horizontal">
                    <img src="${giftImage}" alt="${giftName}" class="gift-icon" onerror="this.style.display='none'">
                    <div class="gift-info">
                      <span class="gift-type-name">${giftName}</span>
                      <span class="gift-type-subname">${window.TranslationSystem ? window.TranslationSystem.translateText('معرف:') : 'معرف:'} ${mapping.giftId}</span>
                    </div>
                  </div>
                `;
              }
              break;

            case 'like':
              eventTypeText = `<span class="badge bg-primary">${window.TranslationSystem ? window.TranslationSystem.translateText('إعجاب') : 'إعجاب'}</span>`;
              eventDetailsText = `
                <div class="gift-type-container horizontal">
                  <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند الوصول إلى') : 'عند الوصول إلى'} ${mapping.likeCount || 10} ${window.TranslationSystem ? window.TranslationSystem.translateText('إعجاب') : 'إعجاب'}</span>
                </div>
              `;
              break;

            case 'comment':
              eventTypeText = `<span class="badge bg-info">${window.TranslationSystem ? window.TranslationSystem.translateText('تعليق') : 'تعليق'}</span>`;
              eventDetailsText = `
                <div class="gift-type-container horizontal">
                  <div class="gift-info">
                    <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند كتابة تعليق:') : 'عند كتابة تعليق:'}</span>
                    <span class="gift-type-subname">${mapping.commentText || ''}</span>
                  </div>
                </div>
              `;
              break;

            case 'follow':
              eventTypeText = `<span class="badge bg-success">${window.TranslationSystem ? window.TranslationSystem.translateText('متابعة') : 'متابعة'}</span>`;
              eventDetailsText = `
                <div class="gift-type-container horizontal">
                  <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند متابعة أي مستخدم') : 'عند متابعة أي مستخدم'}</span>
                </div>
              `;
              break;

            case 'share':
              eventTypeText = `<span class="badge bg-warning">${window.TranslationSystem ? window.TranslationSystem.translateText('مشاركة') : 'مشاركة'}</span>`;
              eventDetailsText = `
                <div class="gift-type-container horizontal">
                  <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند مشاركة أي مستخدم') : 'عند مشاركة أي مستخدم'}</span>
                </div>
              `;
              break;

            case 'join':
              eventTypeText = `<span class="badge bg-secondary">${window.TranslationSystem ? window.TranslationSystem.translateText('انضمام') : 'انضمام'}</span>`;
              if (mapping.joinType === 'specific') {
                eventDetailsText = `
                  <div class="gift-type-container horizontal">
                    <div class="gift-info">
                      <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند انضمام المستخدم:') : 'عند انضمام المستخدم:'}</span>
                      <span class="gift-type-subname">${mapping.specificUsername || ''}</span>
                    </div>
                  </div>
                `;
              } else {
                eventDetailsText = `
                  <div class="gift-type-container horizontal">
                    <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('عند انضمام أي مستخدم') : 'عند انضمام أي مستخدم'}</span>
                  </div>
                `;
              }
              break;

            default:
              eventTypeText = `<span class="badge bg-secondary">${window.TranslationSystem ? window.TranslationSystem.translateText('غير معروف') : 'غير معروف'}</span>`;
              eventDetailsText = `
                <div class="gift-type-container">
                  <span class="gift-type-name">${window.TranslationSystem ? window.TranslationSystem.translateText('نوع حدث غير معروف') : 'نوع حدث غير معروف'}</span>
                </div>
              `;
          }

          // تحديد الأنماط للتعيينات المعطلة
          const disabledClass = !isActive ? 'mapping-disabled' : '';
          const statusBadge = !isActive ? '<span class="badge bg-secondary ms-2">🔒 معطل</span>' : '<span class="badge bg-success ms-2">✅ نشط</span>';

          const row = `
            <tr class="${disabledClass}">
              <td class="text-center">
                <div class="gift-name-container">
                  <span class="gift-name">${mappingName}</span>
                  ${statusBadge}
                </div>
              </td>
              <td class="text-center">
                <div class="gift-info">
                  ${eventTypeText}
                </div>
              </td>
              <td class="text-center">
                ${eventDetailsText}
              </td>
              <td>${actionText}</td>
              <td>${mapping.duration || 5}${window.TranslationSystem ? window.TranslationSystem.translateText('ث') : 'ث'}</td>
              <td>
                <div class="actions-cell">
                  <button class="edit-btn small" data-id="${mapping.id}" ${!isActive ? 'disabled title="التعيين معطل في الوضع المجاني"' : ''}>${window.TranslationSystem ? window.TranslationSystem.translateText('تعديل') : 'تعديل'}</button>
                  <button class="delete-btn small secondary" data-id="${mapping.id}">${window.TranslationSystem ? window.TranslationSystem.translateText('حذف') : 'حذف'}</button>
                  <button class="test-btn small" data-id="${mapping.id}" ${!isActive ? 'disabled title="التعيين معطل في الوضع المجاني"' : ''}>${window.TranslationSystem ? window.TranslationSystem.translateText('اختبار') : 'اختبار'}</button>
                </div>
              </td>
            </tr>
          `;

          tableBody.append(row);
        });

        // إضافة معالجات الأحداث للأزرار
        $('.edit-btn').off('click').on('click', function() {
          const id = $(this).data('id');
          editMapping(id);
        });



        $('.delete-btn').off('click').on('click', function() {
          const id = $(this).data('id');
          deleteMapping(id);
        });

        $('.test-btn').off('click').on('click', function() {
          const id = $(this).data('id');
          const mapping = giftMappings.find(m => String(m.id) === String(id));
          if (mapping) {
            // إضافة تأخير 3 ثوانٍ مع عداد تنازلي
            const button = $(this);
            const originalText = button.text();
            let countdown = 3;

            // تعطيل الزر أثناء العد التنازلي
            button.prop('disabled', true);

            const timer = setInterval(() => {
              button.text(`${originalText} (${countdown})`);
              countdown--;

              if (countdown < 0) {
                clearInterval(timer);
                button.text(originalText);
                button.prop('disabled', false);

                // تنفيذ الاختبار بعد انتهاء العد التنازلي
                testAction(mapping);
              }
            }, 1000);
          } else {
            console.error('لم يتم العثور على التعيين للاختبار:', id);
            showAlert('لم يتم العثور على التعيين المطلوب للاختبار. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
          }
        });

        // تطبيق الترجمة على المحتوى الجديد
        if (window.TranslationSystem) {
          window.TranslationSystem.translateDynamicContent(tableBody[0]);
        }
      } catch (error) {
        console.error('خطأ في تحديث جدول التعيينات:', error);
      }
    }

    // بعد دالة saveMapping()، أضف هذه الدوال الجديدة لمحاكاة ضغط المفاتيح

    // إعداد معالجات أحداث محاكاة ضغط المفاتيح
    function setupKeypressHandlers() {
      // عند تغيير حالة خانة الاختيار المخصصة
      $('#custom-key-checkbox').change(function() {
        if ($(this).is(':checked')) {
          $('#custom-key-input').show();
          $('#custom-key-help').show();
          $('#keypress-select').prop('disabled', true);
        } else {
          $('#custom-key-input').hide().val('');
          $('#custom-key-help').hide();
          $('#keypress-select').prop('disabled', false);
        }
        updateKeyPreview();
      });

      // عند تغيير حالة خانة اختيار الضغط المستمر
      $('#hold-key-checkbox').change(function() {
        if ($(this).is(':checked')) {
          $('#hold-duration-container').show();
        } else {
          $('#hold-duration-container').hide();
        }
        updateKeyPreview();
      });

      // عند تغيير قيمة مربع النص المخصص
      $('#custom-key-input').on('input', function() {
        updateKeyPreview();
      });

      // عند تغيير المفتاح من القائمة المنسدلة
      $('#keypress-select').change(function() {
        updateKeyPreview();
      });

      // عند تغيير مفاتيح التعديل
      $('.modifier-checkbox').change(function() {
        updateKeyPreview();
      });

      // إضافة مفتاح إلى التسلسل
      $('#add-key-to-sequence-btn').click(function() {
        const keyInfo = getKeypressInfo();
        const delay = parseInt($('#keypress-delay').val(), 10) || 0;

        // إضافة معلومات المفتاح والتأخير إلى التسلسل
        keypressSequence.push({
          ...keyInfo,
          delay: delay
        });

        // تحديث عرض التسلسل
        updateKeypressSequenceDisplay();

        // إعادة ضبط حقول الإدخال
        resetKeypressForm();
      });

      // اختبار مفتاح واحد
      $('#test-single-key-btn').click(function() {
        const keyInfo = getKeypressInfo();
        testSingleKeypress(keyInfo);
      });

      // اختبار تسلسل المفاتيح بالكامل
      $('#test-keypress-sequence-btn').click(function() {
        testKeypressSequence();
      });

      // إضافة زر لإضافة مفتاح (يختصر عملية إضافة مفتاح جديد)
      $('#add-keypress-btn').click(function() {
        // إعادة ضبط نموذج إدخال المفتاح وتجهيزه للإضافة
        resetKeypressForm();
      });
    }

    // تهيئة معالجات الأحداث عند تحميل الصفحة
    $(document).ready(function() {
      // إضافة معالج حدث لخيار الضغط المستمر
      $('#hold-key-checkbox').change(function() {
        if ($(this).is(':checked')) {
          $('#hold-duration-container').show();
        } else {
          $('#hold-duration-container').hide();
        }
        updateKeyPreview();
      });
    });

    // تحديث عرض تسلسل المفاتيح في واجهة المستخدم
    function updateKeypressSequenceDisplay() {
      const container = $('#keypress-sequence-list');

      // إذا كان التسلسل فارغًا، أظهر رسالة
      if (keypressSequence.length === 0) {
        container.html(`<div class="text-center text-muted p-2">${window.TranslationSystem ? window.TranslationSystem.translateText('لم يتم إضافة مفاتيح بعد. انقر على زر "إضافة مفتاح" لإضافة مفتاح جديد.') : 'لم يتم إضافة مفاتيح بعد. انقر على زر "إضافة مفتاح" لإضافة مفتاح جديد.'}</div>`);
        return;
      }

      // إنشاء HTML لعرض التسلسل
      let html = '';

      keypressSequence.forEach((item, index) => {
        // تكوين نص المفتاح
        let keyText = getKeyDisplayText(item);

        // إنشاء عنصر HTML للمفتاح
        html += `
          <div class="keypress-item d-flex align-items-center justify-content-between p-2 mb-1 border rounded">
            <div>
              <span class="badge bg-primary me-2">${index + 1}</span>
              <span class="key-text">${keyText}</span>
              ${item.delay > 0 ? `<span class="badge bg-secondary ms-2">${window.TranslationSystem ? window.TranslationSystem.translateText('تأخير:') : 'تأخير:'} ${item.delay} ${window.TranslationSystem ? window.TranslationSystem.translateText('مللي ثانية') : 'مللي ثانية'}</span>` : ''}
            </div>
            <div class="btn-group">
              <button type="button" class="btn btn-sm btn-outline-primary edit-keypress" data-index="${index}">${window.TranslationSystem ? window.TranslationSystem.translateText('تعديل') : 'تعديل'}</button>
              <button type="button" class="btn btn-sm btn-outline-danger delete-keypress" data-index="${index}">${window.TranslationSystem ? window.TranslationSystem.translateText('حذف') : 'حذف'}</button>
            </div>
          </div>
        `;
      });

      // تحديث المحتوى
      container.html(html);

      // إضافة معالجات الأحداث للأزرار
      $('.edit-keypress').click(function() {
        const index = $(this).data('index');
        editKeypressItem(index);
      });

      $('.delete-keypress').click(function() {
        const index = $(this).data('index');
        deleteKeypressItem(index);
      });

      // تطبيق الترجمة على المحتوى الجديد
      if (window.TranslationSystem) {
        window.TranslationSystem.translateDynamicContent(container[0]);
      }
    }

    // إعادة ضبط نموذج إدخال المفتاح
    function resetKeypressForm() {
      $('#keypress-select').val('space').prop('disabled', false);
      $('#custom-key-checkbox').prop('checked', false);
      $('#custom-key-input').hide().val('');
      $('#custom-key-help').hide();
      $('.modifier-checkbox').prop('checked', false);
      $('#keypress-delay').val('0');
      // إعادة ضبط خيارات الضغط المستمر
      $('#hold-key-checkbox').prop('checked', false);
      $('#hold-duration-container').hide();
      $('#hold-duration').val('1000');
      updateKeyPreview();
    }

    // تعديل عنصر في تسلسل المفاتيح
    function editKeypressItem(index) {
      if (index < 0 || index >= keypressSequence.length) return;

      const item = keypressSequence[index];

      // تعبئة النموذج بالبيانات الحالية
      if (item.isCustom) {
        $('#custom-key-checkbox').prop('checked', true);
        $('#custom-key-input').show().val(item.key);
        $('#custom-key-help').show();
        $('#keypress-select').prop('disabled', true);
      } else {
        $('#custom-key-checkbox').prop('checked', false);
        $('#custom-key-input').hide().val('');
        $('#custom-key-help').hide();
        $('#keypress-select').val(item.key).prop('disabled', false);
      }

      // تعيين حالة مفاتيح التعديل
      $('#modifier-ctrl').prop('checked', item.modifiers.ctrl);
      $('#modifier-alt').prop('checked', item.modifiers.alt);
      $('#modifier-shift').prop('checked', item.modifiers.shift);

      // تعيين التأخير
      $('#keypress-delay').val(item.delay || 0);

      // تعيين خيارات الضغط المستمر
      if (item.holdKey) {
        $('#hold-key-checkbox').prop('checked', true);
        $('#hold-duration-container').show();
        $('#hold-duration').val(item.holdDuration || 1000);
      } else {
        $('#hold-key-checkbox').prop('checked', false);
        $('#hold-duration-container').hide();
      }

      // تحديث المعاينة
      updateKeyPreview();

      // حذف العنصر
      keypressSequence.splice(index, 1);

      // تحديث العرض
      updateKeypressSequenceDisplay();

      // التمرير إلى نموذج التعديل
      $('.keypress-editor').get(0).scrollIntoView({ behavior: 'smooth' });
    }

    // حذف عنصر من تسلسل المفاتيح
    function deleteKeypressItem(index) {
      if (index < 0 || index >= keypressSequence.length) return;

      // حذف العنصر
      keypressSequence.splice(index, 1);

      // تحديث العرض
      updateKeypressSequenceDisplay();
    }

    // الحصول على نص العرض للمفتاح
    function getKeyDisplayText(keyInfo) {
      let keyText = '';

      // إضافة مفاتيح التعديل
      const modifiers = [];
      if (keyInfo.modifiers.ctrl) modifiers.push('Ctrl');
      if (keyInfo.modifiers.alt) modifiers.push('Alt');
      if (keyInfo.modifiers.shift) modifiers.push('Shift');

      // إضافة المفتاح الأساسي
      let key = keyInfo.key;
      if (!keyInfo.isCustom) {
        // تحويل المفتاح إلى نص مفهوم
        switch (key) {
          case 'space': key = 'Space'; break;
          case 'enter': key = 'Enter'; break;
          case 'escape': key = 'Escape'; break;
          case 'tab': key = 'Tab'; break;
          case 'arrowup': key = '↑'; break;
          case 'arrowdown': key = '↓'; break;
          case 'arrowleft': key = '←'; break;
          case 'arrowright': key = '→'; break;
          default:
            key = key.charAt(0).toUpperCase() + key.slice(1);
        }
      }

      // تكوين النص النهائي
      if (modifiers.length > 0) {
        keyText = modifiers.join(' + ') + ' + ' + key;
      } else {
        keyText = key;
      }

      // إضافة معلومات الضغط المستمر إذا كان مفعلاً
      if (keyInfo.holdKey) {
        const holdDuration = keyInfo.holdDuration || 1000;
        const holdSeconds = (holdDuration / 1000).toFixed(1);
        const holdText = window.TranslationSystem ? window.TranslationSystem.translateText('ضغط مستمر') : 'ضغط مستمر';
        const secondsText = window.TranslationSystem ? window.TranslationSystem.translateText('ثانية') : 'ثانية';
        keyText += ` (${holdText}: ${holdSeconds} ${secondsText})`;
      }

      return keyText;
    }

    // اختبار ضغط مفتاح واحد
    function testSingleKeypress(keyInfo) {
      // إضافة التأخير إلى معلومات المفتاح
      const delay = parseInt($('#keypress-delay').val(), 10) || 0;
      keyInfo.delay = delay;

      // إرسال طلب لاختبار المفتاح
      socket.emit('testKeypress', {
        keys: [keyInfo]
      });

      // إظهار رسالة
      showAlert('جاري اختبار المفتاح... (قد يستغرق ثواني)', 'info');
    }

    // اختبار تسلسل المفاتيح بالكامل
    function testKeypressSequence() {
      if (keypressSequence.length === 0) {
        showAlert('لا يوجد مفاتيح لاختبارها. أضف مفاتيح أولاً.', 'error');
        return;
      }

      // حساب إجمالي التأخير لتقدير وقت الانتهاء
      let totalDelay = 0;
      keypressSequence.forEach(key => {
        totalDelay += (key.delay || 0);
      });

      // إرسال طلب لاختبار التسلسل
      socket.emit('testKeypress', {
        keys: keypressSequence
      });

      // إظهار رسالة مع وقت التقدير
      const delaySeconds = Math.round(totalDelay / 1000 * 10) / 10;
      const secondsText = window.TranslationSystem ? window.TranslationSystem.translateText('ثانية') : 'ثانية';
      showAlert(`جاري اختبار تسلسل المفاتيح... (مدة التنفيذ التقريبية: ${delaySeconds} ${secondsText})`, 'info');
    }

    // وظيفة تحديث معاينة المفتاح
    function updateKeyPreview() {
      let key = '';

      // تحديد المفتاح المحدد
      if ($('#custom-key-checkbox').is(':checked')) {
        key = $('#custom-key-input').val() || '';

        // Check if we have multiple keys (separated by spaces or commas)
        const multiKeys = key.split(/[ ,]+/).filter(k => k.trim().length > 0);
        if (multiKeys.length > 1) {
          key = multiKeys.map(k => k.trim()).join(' + ');
        }
      } else {
        const selectedKey = $('#keypress-select').val();
        switch (selectedKey) {
          case 'space':
            key = 'Space';
            break;
          case 'enter':
            key = 'Enter';
            break;
          case 'escape':
            key = 'Escape';
            break;
          case 'tab':
            key = 'Tab';
            break;
          default:
            key = selectedKey.charAt(0).toUpperCase() + selectedKey.slice(1);
        }
      }

      // إضافة مفاتيح التعديل
      let modifiers = [];
      if ($('#modifier-ctrl').is(':checked')) modifiers.push('Ctrl');
      if ($('#modifier-alt').is(':checked')) modifiers.push('Alt');
      if ($('#modifier-shift').is(':checked')) modifiers.push('Shift');

      // تكوين نص المعاينة
      let previewText = '';
      if (modifiers.length > 0) {
        previewText = modifiers.join(' + ') + ' + ' + key;
      } else {
        previewText = key;
      }

      // تحديث نص المعاينة
      $('#key-preview-text').text(previewText);
    }

    // وظيفة استخراج معلومات المفتاح لحفظها
    function getKeypressInfo() {
      const keyInfo = {
        key: '',
        modifiers: {
          ctrl: $('#modifier-ctrl').is(':checked'),
          alt: $('#modifier-alt').is(':checked'),
          shift: $('#modifier-shift').is(':checked')
        },
        // إضافة معلومات الضغط المستمر
        holdKey: $('#hold-key-checkbox').is(':checked'),
        holdDuration: $('#hold-key-checkbox').is(':checked') ? parseInt($('#hold-duration').val(), 10) || 1000 : 0
      };

      // تحديد المفتاح المحدد
      if ($('#custom-key-checkbox').is(':checked')) {
        keyInfo.key = $('#custom-key-input').val() || '';
        keyInfo.isCustom = true;
      } else {
        keyInfo.key = $('#keypress-select').val();
        keyInfo.isCustom = false;
      }

      return keyInfo;
    }

    // إضافة تعديل على دالة saveMapping للتعامل مع محاكاة المفاتيح
    const originalSaveMapping = saveMapping;
    saveMapping = function() {
      try {
        // التحقق من حد التعيينات للمستخدمين المجانيين (فقط للتعيينات الجديدة)
        if (!currentEditingId && !checkMappingLimit()) {
          return; // إيقاف العملية إذا تم تجاوز الحد
        }

        // جمع الإجراءات المختارة
        let actions = [];
        $('.action-checkbox:checked').each(function() {
          actions.push($(this).val());
        });

        if (actions.length === 0) {
          showAlert('الرجاء اختيار إجراء واحد على الأقل');
          return;
        }

        // الحصول على نوع الحدث المختار
        const eventType = $('#event-type-select').val();
        const duration = parseInt($('#edit-duration').val(), 10);
        const overlayId = $('#overlay-selector').val();
        const mappingName = $('#edit-mapping-name').val() || '';

        // تجهيز بيانات التعيين الأساسية
        const mappingData = {
          id: currentEditingId || null,  // إعطاء قيمة null عند الإنشاء، والسماح للخادم بإنشاء معرف جديد
          mappingName: mappingName, // اسم التعيين الجديد
          eventType: eventType, // نوع الحدث (هدية، إعجاب، تعليق، متابعة، مشاركة، انضمام)
          actions: actions,
          duration: duration,
          overlayId: overlayId
        };

        console.log('تجهيز بيانات التعيين مع المعرف:', mappingData.id, 'currentEditingId:', currentEditingId);

        // إضافة البيانات الخاصة بكل نوع حدث
        switch (eventType) {
          case 'gift':
            // جمع البيانات من نموذج التعديل
            const giftId = $('#edit-gift').val();

            // التحقق من صحة البيانات
            if (!giftId) {
              showAlert('يرجى اختيار هدية');
              return;
            }

            // الحصول على معلومات الهدية
            let giftName = '', giftImage = '';
            const selectedGift = availableGifts.find(g => {
              const giftId2 = g.id || g.ID || g.Id || '';
              return giftId2 === giftId;
            });

            if (selectedGift) {
              // التعامل مع اسم الهدية
              giftName = selectedGift.name || selectedGift.Name || selectedGift.describe || selectedGift.id;

              // التعامل مع صورة الهدية
              if (selectedGift.imageUrl) {
                giftImage = selectedGift.imageUrl;
              } else if (selectedGift.image && selectedGift.image.url_list && selectedGift.image.url_list.length > 0) {
                giftImage = selectedGift.image.url_list[0];
              } else if (selectedGift.icon && selectedGift.icon.url_list && selectedGift.icon.url_list.length > 0) {
                giftImage = selectedGift.icon.url_list[0];
              }

              // التحقق من صحة الرابط
              if (giftImage && !giftImage.startsWith('http') && !giftImage.startsWith('/')) {
                giftImage = 'https://' + giftImage;
              }
            } else if (giftId === 'any') {
              giftName = 'أي هدية';
              giftImage = '/placeholder.png';
            }

            // إضافة معلومات الهدية إلى بيانات التعيين
            mappingData.giftId = giftId;
            mappingData.giftName = giftName;
            mappingData.giftImage = giftImage;
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          case 'like':
            // جمع بيانات الإعجابات
            const likeCount = parseInt($('#like-count').val(), 10) || 10;
            mappingData.likeCount = likeCount;
            mappingData.eventDetails = `عند الوصول إلى ${likeCount} إعجاب`;
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          case 'comment':
            // جمع بيانات التعليقات
            const commentText = $('#comment-text').val();
            if (!commentText) {
              showAlert('يرجى إدخال نص التعليق');
              return;
            }
            mappingData.commentText = commentText;
            mappingData.eventDetails = `عند كتابة تعليق: ${commentText}`;
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          case 'follow':
            // لا توجد بيانات إضافية للمتابعة
            mappingData.eventDetails = 'عند متابعة أي مستخدم';
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          case 'share':
            // لا توجد بيانات إضافية للمشاركة
            mappingData.eventDetails = 'عند مشاركة أي مستخدم';
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          case 'join':
            // جمع بيانات الانضمام
            const joinType = $('#join-type').val();
            mappingData.joinType = joinType;

            if (joinType === 'specific') {
              const specificUsername = $('#specific-username').val();
              if (!specificUsername) {
                showAlert('يرجى إدخال اسم المستخدم المحدد');
                return;
              }
              mappingData.specificUsername = specificUsername;
              mappingData.eventDetails = `عند انضمام المستخدم: ${specificUsername}`;
            } else {
              mappingData.eventDetails = 'عند انضمام أي مستخدم';
            }
            mappingData.condition = 'any'; // تعيين الشرط على 'any' دائماً
            break;

          default:
            showAlert('نوع حدث غير معروف');
            return;
        }

        // إضافة معلومات محاكاة المفاتيح إذا كان الإجراء هو محاكاة مفتاح
        if (actions.includes('keypress')) {
          // حفظ تسلسل المفاتيح بالكامل بدلاً من مفتاح واحد فقط
          if (keypressSequence.length > 0) {
            mappingData.keypressSequence = keypressSequence;
          } else {
            // إذا لم يكن هناك تسلسل، استخدم المفتاح الحالي
            const currentKey = getKeypressInfo();
            mappingData.keypressSequence = [{
              ...currentKey,
              delay: parseInt($('#keypress-delay').val(), 10) || 0
            }];
          }
        }

        // معالجة الملفات
        const mediaFileInput = document.getElementById('edit-file');
        const soundFileInput = document.getElementById('edit-sound-file');
        let filePromises = [];

        // رفع ملف الوسائط إذا تم اختياره
        if ((actions.includes('image') || actions.includes('video') || actions.includes('custom')) &&
            mediaFileInput && mediaFileInput.files && mediaFileInput.files[0]) {
          const filePromise = new Promise((resolve) => {
            const file = mediaFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                fileType: file.type
              });

              socket.once('fileUploaded', function(response) {
                if (response.success) {
                  mappingData.mediaFilePath = response.filePath;
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        } else if (currentMediaFilePath) {
          mappingData.mediaFilePath = currentMediaFilePath;
        }

        // رفع ملف الصوت إذا تم اختياره
        if (actions.includes('sound') && soundFileInput && soundFileInput.files && soundFileInput.files[0]) {
          const filePromise = new Promise((resolve) => {
            const file = soundFileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
              socket.emit('uploadFile', {
                fileName: file.name,
                fileContent: e.target.result,
                fileType: file.type
              });

              socket.once('fileUploaded', function(response) {
                if (response.success) {
                  mappingData.soundFilePath = response.filePath;
                }
                resolve();
              });
            };
            reader.readAsDataURL(file);
          });
          filePromises.push(filePromise);
        } else if (currentSoundFilePath) {
          mappingData.soundFilePath = currentSoundFilePath;
        }

        // إضافة خيار تشغيل الصوت على الخادم
        mappingData.playSoundOnServer = $('#play-sound-on-server').is(':checked');

        // إرسال البيانات بعد رفع الملفات
        Promise.all(filePromises).then(() => {
          console.log('إرسال بيانات التعيين:', mappingData);

          // حفظ التعيين في نظام الإعدادات المحلي أولاً
          const currentMappings = SettingsManager.get('giftMappings.mappings') || [];

          // إذا كان تعديلاً لتعيين موجود
          if (mappingData.id) {
            const index = currentMappings.findIndex(m => String(m.id) === String(mappingData.id));
            if (index !== -1) {
              currentMappings[index] = mappingData;
            } else {
              // إذا لم يتم العثور على التعيين، أضفه كجديد
              mappingData.id = Date.now().toString();
              currentMappings.push(mappingData);
            }
          } else {
            // تعيين جديد
            mappingData.id = Date.now().toString();
            currentMappings.push(mappingData);
          }

          // حفظ التعيينات المحدثة في نظام الإعدادات مباشرة (بدون استخدام Promise)
          try {
            // حفظ التعيينات محلياً أولاً
            SettingsManager.settings.giftMappings = { mappings: currentMappings };
            SettingsManager.saveToLocalStorage();

            // تأكد من أن معرف التعيين الحالي محفوظ بشكل صحيح
            if (currentEditingId) {
              mappingData.id = currentEditingId;
            }

            console.log('حفظ التعيين بمعرف:', mappingData.id, 'هل هو تعديل؟', !!currentEditingId);

            // ثم إرسال بيانات التعيين إلى الخادم
            socket.emit('saveGiftMapping', mappingData);

            // معالج استجابة الخادم للحفظ
            socket.once('mappingSaved', function(response) {
              console.log('تم حفظ التعيين بنجاح:', response);
              showAlert('تم حفظ التعيين بنجاح', 'success');

              // تحديث قائمة التعيينات
              socket.emit('getGiftMappings');

              // إغلاق النافذة المنبثقة
              document.getElementById('mappingModal').querySelector('.btn-close').click();
            });
          } catch (error) {
            console.error('خطأ في حفظ التعيين محلياً:', error);
            showAlert('حدث خطأ أثناء حفظ التعيين محلياً', 'error');
          }

          // معالج خطأ الحفظ
          socket.once('errorMessage', function(error) {
            console.error('خطأ في حفظ التعيين:', error);

            // إذا كان الخطأ أن التعيين غير موجود، فربما تم حذفه من قبل
            if (error.message === 'التعيين غير موجود' && currentEditingId) {
              showAlert('لا يمكن تعديل هذا التعيين لأنه ربما تم حذفه. سيتم إنشاء تعيين جديد.');
              // حاول إنشاء تعيين جديد بدلًا من التعديل
              currentEditingId = null;
              mappingData.id = null;  // تأكد من إلغاء المعرف السابق

              // ارسل الطلب مرة أخرى، لكن بدون معرف (كتعيين جديد)
              console.log('إعادة محاولة حفظ التعيين كتعيين جديد');
              socket.emit('saveGiftMapping', mappingData);

              // إضافة معالج للإستجابة مرة أخرى
              socket.once('mappingSaved', function(response) {
                console.log('تم حفظ التعيين الجديد بنجاح:', response);
                showAlert('تم حفظ التعيين كجديد بنجاح', 'success');

                // تحديث قائمة التعيينات
                socket.emit('getGiftMappings');

                // إغلاق النافذة المنبثقة
                document.getElementById('mappingModal').querySelector('.btn-close').click();
              });

              return;
            }

            showAlert('حدث خطأ أثناء حفظ التعيين: ' + error.message);
          });
        }).catch(error => {
          console.error('خطأ في رفع الملفات:', error);
          showAlert('حدث خطأ أثناء رفع الملفات: ' + error.message);
        });
      } catch (error) {
        console.error('خطأ في حفظ التعيين:', error);
        showAlert('حدث خطأ أثناء حفظ التعيين: ' + error.message);
      }
    }

    function simulateKeyPressSequence(keys) {
      console.log('محاكاة تسلسل ضغط المفاتيح:', keys);

      try {
        let cumulativeDelay = 0;
        keys.forEach(keyInfo => {
          const delay = typeof keyInfo.delay === 'number' ? keyInfo.delay : 0;

          setTimeout(() => {
            const key = keyInfo.key.trim();
            let command = '';

            // إضافة المفاتيح المعدلة إذا كانت موجودة
            if (keyInfo.modifiers) {
              if (keyInfo.modifiers.ctrl) command += 'CTRL+';
              if (keyInfo.modifiers.alt) command += 'ALT+';
              if (keyInfo.modifiers.shift) command += 'SHIFT+';
            }

            command += key;
            console.log(`محاولة ضغط المفتاح: ${command}`);

            // استخدام SendInput.exe لمحاكاة المفتاح
            exec(`gSendInput.exe "${command}"`, (error) => {
              if (error) {
                console.error(`خطأ في محاكاة المفتاح: ${error.message}`);
              } else {
                console.log(`تم ضغط المفتاح ${command} بنجاح`);
              }
            });

          }, cumulativeDelay);

          cumulativeDelay += delay;
        });


        return true;
      } catch (err) {
        console.error('خطأ في محاكاة ضغط المفاتيح:', err);
        return false;
      }
    }

    // ===== نظام تحديد التعيينات للمستخدمين المجانيين =====

    // متغيرات عامة لحالة المستخدم والاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

          // التأكد من تهيئة giftMappings
          if (typeof giftMappings === 'undefined') {
            giftMappings = [];
          }

          updateMappingLimitUI();
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;

        // التأكد من تهيئة giftMappings
        if (typeof giftMappings === 'undefined') {
          giftMappings = [];
        }

        updateMappingLimitUI();
      }
    }

    // التحقق من حد التعيينات
    function checkMappingLimit() {
      // إذا كان لديه اشتراك نشط، لا يوجد حد
      if (hasActiveSubscription) {
        return true;
      }

      // عد التعيينات النشطة فقط
      const activeMappingsCount = getActiveMappingsCount();
      const freeLimit = 5;

      if (activeMappingsCount >= freeLimit) {
        showMappingLimitDialog();
        return false;
      }

      return true;
    }

    // حساب عدد التعيينات النشطة
    function getActiveMappingsCount() {
      if (!giftMappings || !Array.isArray(giftMappings)) {
        return 0;
      }

      if (hasActiveSubscription) {
        // المشتركون: جميع التعيينات نشطة
        return giftMappings.length;
      } else {
        // المستخدمون المجانيون: أول 5 تعيينات فقط نشطة
        return Math.min(giftMappings.length, 5);
      }
    }

    // حساب عدد التعيينات المعطلة
    function getDisabledMappingsCount() {
      if (!giftMappings || !Array.isArray(giftMappings) || hasActiveSubscription) {
        return 0;
      }

      return Math.max(0, giftMappings.length - 5);
    }

    // التحقق من كون التعيين نشط أم معطل
    function isMappingActive(mappingIndex) {
      if (hasActiveSubscription) {
        return true; // المشتركون: جميع التعيينات نشطة
      }

      return mappingIndex < 5; // المجانيون: أول 5 فقط نشطة
    }

    // عرض رسالة تجاوز الحد
    function showMappingLimitDialog() {
      const modal = `
        <div class="modal fade" id="limitModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content" style="background-color: var(--section-bg); color: var(--text-color);">
              <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title">🔒 تم الوصول للحد الأقصى</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <div class="text-center">
                  <div style="font-size: 3rem; margin-bottom: 20px;">⚡</div>
                  <h4 style="color: var(--primary-color); margin-bottom: 15px;">الوضع المجاني يسمح بـ 5 تعيينات نشطة فقط</h4>
                  <p style="color: var(--text-secondary); margin-bottom: 25px;">
                    لديك حالياً <strong>${getActiveMappingsCount()}</strong> تعيينات نشطة من أصل <strong>5</strong> مسموحة في الوضع المجاني.
                    ${getDisabledMappingsCount() > 0 ?
                      '<br><strong style="color: #dc3545;">' + getDisabledMappingsCount() + ' تعيينات معطلة</strong> - اشترك لتفعيلها!' :
                      ''
                    }
                  </p>
                  <div style="background: var(--table-header-bg); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h5 style="color: var(--primary-color); margin-bottom: 15px;">🚀 اشترك للحصول على:</h5>
                    <ul style="text-align: right; margin: 0; padding-right: 20px;">
                      <li>تعيينات غير محدودة</li>
                      <li>جميع ميزات الألعاب</li>
                      <li>تخصيص كامل للواجهة</li>
                      <li>دعم فني على مدار الساعة</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="/subscriptions.html" class="btn btn-primary">اشترك الآن - $10/شهر</a>
              </div>
            </div>
          </div>
        </div>
      `;

      // إزالة النافذة السابقة إذا كانت موجودة
      $('#limitModal').remove();

      // إضافة النافذة الجديدة
      $('body').append(modal);

      // عرض النافذة
      new bootstrap.Modal(document.getElementById('limitModal')).show();
    }

    // تحديث واجهة المستخدم لإظهار حالة الحد
    function updateMappingLimitUI() {
      const totalCount = giftMappings ? giftMappings.length : 0;
      const activeCount = getActiveMappingsCount();
      const disabledCount = getDisabledMappingsCount();
      const freeLimit = 5;

      // إزالة أي مؤشر سابق
      $('.mapping-limit-indicator').remove();

      if (!hasActiveSubscription) {
        // إضافة مؤشر الحد للمستخدمين المجانيين
        let statusText = `<strong>الوضع المجاني:</strong> ${activeCount}/${freeLimit} تعيينات نشطة`;
        let statusColor = activeCount >= freeLimit ? '#dc3545' : '#856404';
        let bgColor = activeCount >= freeLimit ? 'rgba(220, 53, 69, 0.1)' : 'rgba(255, 193, 7, 0.1)';
        let borderColor = activeCount >= freeLimit ? 'rgba(220, 53, 69, 0.2)' : 'rgba(255, 193, 7, 0.2)';

        if (disabledCount > 0) {
          statusText += ` <span style="color: #dc3545;">(${disabledCount} معطلة)</span>`;
        }

        let detailText = '';
        if (activeCount >= freeLimit) {
          detailText = '<br><small>تم الوصول للحد الأقصى. <a href="/subscriptions.html" style="color: #dc3545; text-decoration: underline;">اشترك للحصول على تعيينات غير محدودة</a></small>';
        } else {
          detailText = '<br><small>متبقي ' + (freeLimit - activeCount) + ' تعيينات نشطة</small>';
        }

        if (disabledCount > 0) {
          detailText += '<br><small style="color: #dc3545;">⚠️ ' + disabledCount + ' تعيينات معطلة - <a href="/subscriptions.html" style="color: #dc3545; text-decoration: underline;">اشترك لتفعيلها</a></small>';
        }

        const limitIndicator = `
          <div class="mapping-limit-indicator" style="
            background: ${bgColor};
            border: 1px solid ${borderColor};
            color: ${statusColor};
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
          ">
            ${statusText}
            ${detailText}
          </div>
        `;

        $('.buttons-container').before(limitIndicator);

        // تعطيل زر الإضافة إذا تم الوصول للحد
        if (activeCount >= freeLimit) {
          $('#add-mapping-btn').prop('disabled', true).text('تم الوصول للحد الأقصى');
        } else {
          $('#add-mapping-btn').prop('disabled', false).text('إضافة ربط جديد');
        }
      } else {
        // للمستخدمين المشتركين
        const premiumIndicator = `
          <div class="mapping-limit-indicator" style="
            background: rgba(25, 135, 84, 0.1);
            border: 1px solid rgba(25, 135, 84, 0.2);
            color: #198754;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
          ">
            <strong>⚡ الباقة الشاملة:</strong> تعيينات غير محدودة (${totalCount} تعيين حالياً)
          </div>
        `;

        $('.buttons-container').before(premiumIndicator);
        $('#add-mapping-btn').prop('disabled', false).text('إضافة ربط جديد');
      }
    }

    // الاستماع لتغييرات حالة المصادقة
    window.addEventListener('authStateChanged', (event) => {
      currentUser = event.detail.user;
      if (currentUser) {
        loadUserSubscription();
      } else {
        hasActiveSubscription = false;
        // التأكد من تهيئة giftMappings
        if (typeof giftMappings === 'undefined') {
          giftMappings = [];
        }
        updateMappingLimitUI();
      }
    });



    // تحديث واجهة المستخدم عند تحديث التعيينات
    const originalUpdateMappingsTable = updateMappingsTable;
    updateMappingsTable = function(mappings) {
      originalUpdateMappingsTable(mappings);
      // تحديث مؤشر الحد بعد تحديث الجدول
      setTimeout(updateMappingLimitUI, 100);
    };

    // ===========================
    // التحكم في إظهار/إخفاء قسم اختبار الأحداث
    // ===========================

    // زر التحكم في إظهار/إخفاء قسم اختبار الأحداث
    $('#toggle-event-testing').on('click', function() {
      const section = $('#event-testing-section');
      const icon = $('#toggle-icon');
      const text = $('#toggle-text');

      if (section.is(':visible')) {
        // إخفاء القسم
        section.slideUp(300);
        icon.text('👁️');
        text.text('إظهار');
        localStorage.setItem('eventTestingVisible', 'false');
      } else {
        // إظهار القسم
        section.slideDown(300);
        icon.text('🙈');
        text.text('إخفاء');
        localStorage.setItem('eventTestingVisible', 'true');
      }
    });

    // استرجاع حالة القسم من localStorage عند تحميل الصفحة
    $(document).ready(function() {
      const isVisible = localStorage.getItem('eventTestingVisible') === 'true';
      const section = $('#event-testing-section');
      const icon = $('#toggle-icon');
      const text = $('#toggle-text');

      if (isVisible) {
        section.show();
        icon.text('🙈');
        text.text('إخفاء');
      } else {
        section.hide();
        icon.text('👁️');
        text.text('إظهار');
      }
    });

    // ===========================
    // نظام اختبار الأحداث
    // ===========================

    // تحديث قائمة الهدايا في اختبار الهدايا
    function updateTestGiftsList() {
      const giftSelect = $('#test-gift-select');
      giftSelect.empty();
      giftSelect.append('<option value="">اختر هدية...</option>');

      if (availableGifts && Array.isArray(availableGifts)) {
        availableGifts.forEach(gift => {
          const option = $('<option></option>')
            .attr('value', gift.id || gift.name)
            .text(`${gift.name} (${gift.diamondCount || 1} 💎)`)
            .data('gift', gift);
          giftSelect.append(option);
        });
      }
    }

    // تحديث قائمة الهدايا عند تحميل البيانات
    socket.on('availableGifts', function(data) {
      // الكود الموجود...
      updateTestGiftsList();
    });

    // معالجة نتائج محاكاة الأحداث
    socket.on('eventSimulated', function(data) {
      if (data.success) {
        showToast(`✅ ${data.message}`);
      } else {
        showAlert(`❌ فشل في محاكاة الحدث: ${data.error}`, 'error');
      }
    });

    // اختبار الهدايا
    $('#test-gift-btn').on('click', function() {
      const giftId = $('#test-gift-select').val();
      const username = $('#test-gift-username').val().trim();
      const repeatCount = parseInt($('#test-gift-repeat').val()) || 1;

      if (!giftId) {
        showAlert('يرجى اختيار هدية للاختبار', 'error');
        return;
      }

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const selectedGift = availableGifts.find(g => (g.id || g.name) === giftId);
      const eventData = {
        giftId: giftId,
        giftName: selectedGift ? selectedGift.name : giftId,
        nickname: username,
        uniqueId: username,
        repeatCount: repeatCount,
        diamondCount: selectedGift ? selectedGift.diamondCount : 1
      };

      console.log('إرسال اختبار هدية:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'gift',
        eventData: eventData
      });

      showToast(`🎁 جاري اختبار هدية ${eventData.giftName} من ${username}...`);
    });

    // اختبار التعليقات
    $('#test-comment-btn').on('click', function() {
      const comment = $('#test-comment-text').val().trim();
      const username = $('#test-comment-username').val().trim();

      if (!comment) {
        showAlert('يرجى إدخال نص التعليق', 'error');
        return;
      }

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const eventData = {
        comment: comment,
        nickname: username,
        uniqueId: username
      };

      console.log('إرسال اختبار تعليق:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'comment',
        eventData: eventData
      });

      showToast(`💬 جاري اختبار تعليق من ${username}...`);
    });

    // اختبار الإعجابات
    $('#test-like-btn').on('click', function() {
      const likeCount = parseInt($('#test-like-count').val()) || 1;
      const username = $('#test-like-username').val().trim();

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const eventData = {
        likeCount: likeCount,
        totalLikeCount: likeCount,
        nickname: username,
        uniqueId: username
      };

      console.log('إرسال اختبار إعجاب:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'like',
        eventData: eventData
      });

      showToast(`❤️ جاري اختبار ${likeCount} إعجاب من ${username}...`);
    });

    // اختبار المتابعة
    $('#test-follow-btn').on('click', function() {
      const username = $('#test-follow-username').val().trim();

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const eventData = {
        nickname: username,
        uniqueId: username
      };

      console.log('إرسال اختبار متابعة:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'follow',
        eventData: eventData
      });

      showToast(`👥 جاري اختبار متابعة من ${username}...`);
    });

    // اختبار المشاركة
    $('#test-share-btn').on('click', function() {
      const username = $('#test-share-username').val().trim();

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const eventData = {
        nickname: username,
        uniqueId: username
      };

      console.log('إرسال اختبار مشاركة:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'share',
        eventData: eventData
      });

      showToast(`📤 جاري اختبار مشاركة من ${username}...`);
    });

    // اختبار الانضمام
    $('#test-join-btn').on('click', function() {
      const username = $('#test-join-username').val().trim();

      if (!username) {
        showAlert('يرجى إدخال اسم المستخدم', 'error');
        return;
      }

      const eventData = {
        nickname: username,
        uniqueId: username
      };

      console.log('إرسال اختبار انضمام:', eventData);
      socket.emit('simulateEvent', {
        eventType: 'join',
        eventData: eventData
      });

      showToast(`🚪 جاري اختبار انضمام من ${username}...`);
    });

    // تحديث قائمة الهدايا عند تحميل الصفحة
    setTimeout(updateTestGiftsList, 1000);

  </script>

  <!-- Firebase Integration -->
  <script type="module" src="/js/firebase-config.js"></script>

  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>